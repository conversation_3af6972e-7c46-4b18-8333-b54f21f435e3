<!DOCTYPE html>
<!-- saved from url=(0018)https://encurt.ar/ -->
<html lang="pt-BR" class="dark"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<link href="https://encurt.ar/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png">
<link href="https://encurt.ar/apple-touch-icon.png" rel="apple-touch-icon">
<link href="https://encurt.ar/site.webmanifest" rel="manifest">
<link href="https://encurt.ar/favicon.ico" rel="shortcut icon">
<meta content="width=device-width, initial-scale=1.0" name="viewport">
<title>encurt.ar - Encurtador de Links Gratuito e Rápido</title>
<!-- SEO Meta Tags -->
<meta content="Encurte seus links gratuitamente! Transforme URLs longas em links curtos e compartilháveis. Rápido, seguro e fácil de usar." name="description">
<meta content="encurtador de links, URL curta, encurtar link, link shortener, encurt.ar, compartilhar links" name="keywords">
<meta content="encurt.ar" name="author">
<meta content="index, follow" name="robots">
<!-- PWA Meta Tags -->
<meta content="#0ea5e9" name="theme-color">
<link href="https://encurt.ar/manifest.json" rel="manifest">
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="default" name="apple-mobile-web-app-status-bar-style">
<meta content="encurt.ar" name="apple-mobile-web-app-title">
<link href="https://encurt.ar/icon-192.png" rel="apple-touch-icon">
<!-- Open Graph / Facebook -->
<meta content="website" property="og:type">
<meta content="https://encurt.ar/" property="og:url">
<meta content="encurt.ar - Encurtador de Links Gratuito" property="og:title">
<meta content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro." property="og:description">
<meta content="https://encurt.ar/og-image.png" property="og:image">
<!-- Twitter -->
<meta content="summary_large_image" property="twitter:card">
<meta content="https://encurt.ar/" property="twitter:url">
<meta content="encurt.ar - Encurtador de Links Gratuito" property="twitter:title">
<meta content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro." property="twitter:description">
<meta content="https://encurt.ar/og-image.png" property="twitter:image">
<!-- Google AdSense -->
<meta content="ca-pub-****************" name="google-adsense-account">
<!-- Preload recursos críticos -->
<link href="https://fonts.googleapis.com/" rel="preconnect">
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect">
<link href="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/saved_resource" rel="preconnect">
<!-- Outros recursos -->
<link href="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/css2" rel="stylesheet">
<script src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/saved_resource"></script>
<script>
    // Configuração do Tailwind para dark mode
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          animation: {
            'gradient': 'gradient 8s linear infinite',
            'float': 'float 6s ease-in-out infinite',
            'pulse-slow': 'pulse 3s ease-in-out infinite',
            'slide-up': 'slideUp 0.5s ease-out',
            'fade-in': 'fadeIn 0.5s ease-out',
          },
          keyframes: {
            gradient: {
              '0%, 100%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
            },
            float: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-20px)' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
          },
          backdropBlur: {
            xs: '2px',
          }
        }
      }
    }
  </script>
<!-- Chart.js para gráficos -->
<script src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/chart.js.download"></script>
<!-- QRCode.js para gerar QR codes -->
<script src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/qrcode.min.js.download"></script>
<style>
    /* Estilos customizados para glassmorphism e animações */
    .glass {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .dark .glass {
      background: rgba(30, 41, 59, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .gradient-bg {
      background: linear-gradient(-45deg, #0ea5e9, #6366f1, #8b5cf6, #3b82f6);
      background-size: 400% 400%;
      animation: gradient 15s ease infinite;
    }
    
    .tooltip {
      position: relative;
    }
    
    .tooltip-text {
      visibility: hidden;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      transform: translateX(-50%);
      background-color: #1f2937;
      color: white;
      text-align: center;
      padding: 5px 10px;
      border-radius: 6px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }
    
    .loader-dots {
      display: inline-flex;
      gap: 4px;
    }
    
    .loader-dots div {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: currentColor;
      animation: loader-bounce 1.4s infinite ease-in-out both;
    }
    
    .loader-dots div:nth-child(1) { animation-delay: -0.32s; }
    .loader-dots div:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes loader-bounce {
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1); }
    }
    
    /* Transições suaves para o dark mode */
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
    
    /* Scrollbar customizada */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    
    .dark ::-webkit-scrollbar-track {
      background: #1f2937;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    
    /* Animação para o botão de copiar */
    .copy-success {
      animation: copyPulse 0.6s ease;
    }
    
    @keyframes copyPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.2); }
      100% { transform: scale(1); }
    }
    
    /* Estilo para o gráfico */
    .chart-container {
      position: relative;
      height: 250px;
      width: 100%;
    }
  </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0px}.inset-y-0{top:0px;bottom:0px}.right-0{right:0px}.right-4{right:1rem}.top-4{top:1rem}.z-50{z-index:50}.mx-4{margin-left:1rem;margin-right:1rem}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.ml-3{margin-left:0.75rem}.mt-1{margin-top:0.25rem}.mt-2{margin-top:0.5rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.block{display:block}.inline-block{display:inline-block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.h-full{height:100%}.max-h-96{max-height:24rem}.max-h-\[90vh\]{max-height:90vh}.max-h-64{max-height:16rem}.min-h-screen{min-height:100vh}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-8{width:2rem}.w-full{width:100%}.max-w-4xl{max-width:56rem}.max-w-lg{max-width:32rem}.max-w-md{max-width:28rem}.max-w-2xl{max-width:42rem}.flex-1{flex:1 1 0%}.flex-grow{flex-grow:1}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}.animate-fade-in{animation:fadeIn 0.5s ease-out}@keyframes slideUp{0%{transform:translateY(20px);opacity:0}100%{transform:translateY(0);opacity:1}}.animate-slide-up{animation:slideUp 0.5s ease-out}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.cursor-pointer{cursor:pointer}.grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-3{gap:0.75rem}.gap-4{gap:1rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-3 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.75rem * var(--tw-space-x-reverse));margin-left:calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.break-all{word-break:break-all}.rounded{border-radius:0.25rem}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.rounded-l-lg{border-top-left-radius:0.5rem;border-bottom-left-radius:0.5rem}.rounded-r-lg{border-top-right-radius:0.5rem;border-bottom-right-radius:0.5rem}.border{border-width:1px}.border-0{border-width:0px}.border-b-2{border-bottom-width:2px}.border-r-0{border-right-width:0px}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.border-sky-500{--tw-border-opacity:1;border-color:rgb(14 165 233 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-300{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity, 1))}.bg-green-500{--tw-bg-opacity:1;background-color:rgb(34 197 94 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-sky-50{--tw-bg-opacity:1;background-color:rgb(240 249 255 / var(--tw-bg-opacity, 1))}.bg-sky-500{--tw-bg-opacity:1;background-color:rgb(14 165 233 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-yellow-50{--tw-bg-opacity:1;background-color:rgb(254 252 232 / var(--tw-bg-opacity, 1))}.bg-red-50{--tw-bg-opacity:1;background-color:rgb(254 242 242 / var(--tw-bg-opacity, 1))}.bg-opacity-50{--tw-bg-opacity:0.5}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-gray-600{--tw-gradient-from:#4b5563 var(--tw-gradient-from-position);--tw-gradient-to:rgb(75 85 99 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-green-500{--tw-gradient-from:#22c55e var(--tw-gradient-from-position);--tw-gradient-to:rgb(34 197 94 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-red-500{--tw-gradient-from:#ef4444 var(--tw-gradient-from-position);--tw-gradient-to:rgb(239 68 68 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-sky-50{--tw-gradient-from:#f0f9ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(240 249 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-sky-500{--tw-gradient-from:#0ea5e9 var(--tw-gradient-from-position);--tw-gradient-to:rgb(14 165 233 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-sky-600{--tw-gradient-from:#0284c7 var(--tw-gradient-from-position);--tw-gradient-to:rgb(2 132 199 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-blue-600{--tw-gradient-to:#2563eb var(--tw-gradient-to-position)}.to-gray-700{--tw-gradient-to:#374151 var(--tw-gradient-to-position)}.to-green-600{--tw-gradient-to:#16a34a var(--tw-gradient-to-position)}.to-indigo-50{--tw-gradient-to:#eef2ff var(--tw-gradient-to-position)}.to-indigo-600{--tw-gradient-to:#4f46e5 var(--tw-gradient-to-position)}.to-indigo-700{--tw-gradient-to:#4338ca var(--tw-gradient-to-position)}.to-red-600{--tw-gradient-to:#dc2626 var(--tw-gradient-to-position)}.to-sky-600{--tw-gradient-to:#0284c7 var(--tw-gradient-to-position)}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.p-2{padding:0.5rem}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-2{padding-left:0.5rem;padding-right:0.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-8{padding-top:2rem;padding-bottom:2rem}.pr-10{padding-right:2.5rem}.pr-3{padding-right:0.75rem}.text-center{text-align:center}.font-mono{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace}.text-2xl{font-size:1.5rem;line-height:2rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-green-600{--tw-text-opacity:1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-sky-400{--tw-text-opacity:1;color:rgb(56 189 248 / var(--tw-text-opacity, 1))}.text-sky-500{--tw-text-opacity:1;color:rgb(14 165 233 / var(--tw-text-opacity, 1))}.text-sky-600{--tw-text-opacity:1;color:rgb(2 132 199 / var(--tw-text-opacity, 1))}.text-transparent{color:transparent}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-yellow-800{--tw-text-opacity:1;color:rgb(133 77 14 / var(--tw-text-opacity, 1))}.text-red-800{--tw-text-opacity:1;color:rgb(153 27 27 / var(--tw-text-opacity, 1))}.placeholder-gray-400::placeholder{--tw-placeholder-opacity:1;color:rgb(156 163 175 / var(--tw-placeholder-opacity, 1))}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-inner{--tw-shadow:inset 0 2px 4px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:inset 0 2px 4px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.hover\:-translate-y-0\.5:hover{--tw-translate-y:-0.125rem;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:scale-110:hover{--tw-scale-x:1.1;--tw-scale-y:1.1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))}.hover\:bg-gray-400:hover{--tw-bg-opacity:1;background-color:rgb(156 163 175 / var(--tw-bg-opacity, 1))}.hover\:bg-green-600:hover{--tw-bg-opacity:1;background-color:rgb(22 163 74 / var(--tw-bg-opacity, 1))}.hover\:bg-sky-600:hover{--tw-bg-opacity:1;background-color:rgb(2 132 199 / var(--tw-bg-opacity, 1))}.hover\:from-blue-600:hover{--tw-gradient-from:#2563eb var(--tw-gradient-from-position);--tw-gradient-to:rgb(37 99 235 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:from-gray-700:hover{--tw-gradient-from:#374151 var(--tw-gradient-from-position);--tw-gradient-to:rgb(55 65 81 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:from-green-600:hover{--tw-gradient-from:#16a34a var(--tw-gradient-from-position);--tw-gradient-to:rgb(22 163 74 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:from-red-600:hover{--tw-gradient-from:#dc2626 var(--tw-gradient-from-position);--tw-gradient-to:rgb(220 38 38 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:from-sky-600:hover{--tw-gradient-from:#0284c7 var(--tw-gradient-from-position);--tw-gradient-to:rgb(2 132 199 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:to-blue-700:hover{--tw-gradient-to:#1d4ed8 var(--tw-gradient-to-position)}.hover\:to-gray-800:hover{--tw-gradient-to:#1f2937 var(--tw-gradient-to-position)}.hover\:to-green-700:hover{--tw-gradient-to:#15803d var(--tw-gradient-to-position)}.hover\:to-indigo-700:hover{--tw-gradient-to:#4338ca var(--tw-gradient-to-position)}.hover\:to-red-700:hover{--tw-gradient-to:#b91c1c var(--tw-gradient-to-position)}.hover\:to-sky-700:hover{--tw-gradient-to:#0369a1 var(--tw-gradient-to-position)}.hover\:text-gray-700:hover{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.hover\:text-sky-800:hover{--tw-text-opacity:1;color:rgb(7 89 133 / var(--tw-text-opacity, 1))}.hover\:underline:hover{-webkit-text-decoration-line:underline;text-decoration-line:underline}.hover\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.focus\:border-sky-500:focus{--tw-border-opacity:1;border-color:rgb(14 165 233 / var(--tw-border-opacity, 1))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-sky-400:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(56 189 248 / var(--tw-ring-opacity, 1))}.focus\:ring-sky-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(14 165 233 / var(--tw-ring-opacity, 1))}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px}.dark\:border-gray-600:is(.dark *){--tw-border-opacity:1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1))}.dark\:border-gray-700:is(.dark *){--tw-border-opacity:1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1))}.dark\:bg-blue-900\/20:is(.dark *){background-color:rgb(30 58 138 / 0.2)}.dark\:bg-gray-700:is(.dark *){--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-800:is(.dark *){--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.dark\:bg-gray-800\/50:is(.dark *){background-color:rgb(31 41 55 / 0.5)}.dark\:bg-green-900\/20:is(.dark *){background-color:rgb(20 83 45 / 0.2)}.dark\:bg-purple-900\/20:is(.dark *){background-color:rgb(88 28 135 / 0.2)}.dark\:bg-sky-900\/20:is(.dark *){background-color:rgb(12 74 110 / 0.2)}.dark\:bg-yellow-900\/20:is(.dark *){background-color:rgb(113 63 18 / 0.2)}.dark\:bg-red-900\/20:is(.dark *){background-color:rgb(127 29 29 / 0.2)}.dark\:from-sky-400:is(.dark *){--tw-gradient-from:#38bdf8 var(--tw-gradient-from-position);--tw-gradient-to:rgb(56 189 248 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.dark\:from-sky-900\/20:is(.dark *){--tw-gradient-from:rgb(12 74 110 / 0.2) var(--tw-gradient-from-position);--tw-gradient-to:rgb(12 74 110 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.dark\:to-indigo-500:is(.dark *){--tw-gradient-to:#6366f1 var(--tw-gradient-to-position)}.dark\:to-indigo-900\/20:is(.dark *){--tw-gradient-to:rgb(49 46 129 / 0.2) var(--tw-gradient-to-position)}.dark\:text-blue-400:is(.dark *){--tw-text-opacity:1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.dark\:text-gray-100:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246 / var(--tw-text-opacity, 1))}.dark\:text-gray-200:is(.dark *){--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.dark\:text-gray-300:is(.dark *){--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.dark\:text-gray-400:is(.dark *){--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.dark\:text-gray-500:is(.dark *){--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.dark\:text-green-400:is(.dark *){--tw-text-opacity:1;color:rgb(74 222 128 / var(--tw-text-opacity, 1))}.dark\:text-purple-400:is(.dark *){--tw-text-opacity:1;color:rgb(192 132 252 / var(--tw-text-opacity, 1))}.dark\:text-red-400:is(.dark *){--tw-text-opacity:1;color:rgb(248 113 113 / var(--tw-text-opacity, 1))}.dark\:text-sky-300:is(.dark *){--tw-text-opacity:1;color:rgb(125 211 252 / var(--tw-text-opacity, 1))}.dark\:text-sky-400:is(.dark *){--tw-text-opacity:1;color:rgb(56 189 248 / var(--tw-text-opacity, 1))}.dark\:text-yellow-200:is(.dark *){--tw-text-opacity:1;color:rgb(254 240 138 / var(--tw-text-opacity, 1))}.dark\:text-yellow-400:is(.dark *){--tw-text-opacity:1;color:rgb(250 204 21 / var(--tw-text-opacity, 1))}.dark\:text-red-200:is(.dark *){--tw-text-opacity:1;color:rgb(254 202 202 / var(--tw-text-opacity, 1))}.dark\:placeholder-gray-500:is(.dark *)::placeholder{--tw-placeholder-opacity:1;color:rgb(107 114 128 / var(--tw-placeholder-opacity, 1))}.dark\:hover\:bg-gray-600:hover:is(.dark *){--tw-bg-opacity:1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.dark\:hover\:text-gray-200:hover:is(.dark *){--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.dark\:hover\:text-sky-300:hover:is(.dark *){--tw-text-opacity:1;color:rgb(125 211 252 / var(--tw-text-opacity, 1))}@media (min-width: 640px){.sm\:mb-8{margin-bottom:2rem}.sm\:p-8{padding:2rem}.sm\:text-5xl{font-size:3rem;line-height:1}.sm\:text-base{font-size:1rem;line-height:1.5rem}}</style></head>
<body class="min-h-screen flex flex-col items-center justify-center p-4 transition-colors duration-300 gradient-bg">
<!-- Botão Dark Mode -->
<button class="fixed top-4 right-4 p-3 rounded-full glass hover:scale-110 transition-transform duration-300 z-50 tooltip" id="darkModeToggle" aria-label="Alternar entre modo claro e escuro">
<svg class="w-6 h-6 text-gray-800 dark:text-yellow-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
<path class="sun hidden" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
<path class="moon" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
<span class="tooltip-text">Alternar Tema</span>
</button>
<!-- Container Principal -->
<div class="glass p-6 sm:p-8 rounded-2xl shadow-2xl w-full max-w-lg animate-fade-in">
<!-- Header -->
<header class="text-center mb-6 sm:mb-8">
<h1 class="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700 dark:from-sky-400 dark:to-indigo-500 animate-slide-up">
        encurt.<span class="text-sky-400 dark:text-sky-300">ar</span>
</h1>
<p class="text-gray-600 dark:text-gray-300 mt-2 text-sm sm:text-base animate-fade-in animation-delay-100">
        Seu novo encurtador de links favorito! ✨
      </p>
</header>
<!-- Mensagens -->
<div class="mb-4 space-y-2" id="messageContainer"></div>
<!-- Seção de Autenticação -->
<section class="mb-6" id="authSection">
<div class="hidden" id="authForms">
<!-- Formulário de Login -->
<form class="space-y-4 mb-4" id="loginForm">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">Login</h3>
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300" for="loginEmail">Email</label>
<input class="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-300" id="loginEmail" name="loginEmail" required="" type="email">
<div class="mt-1 text-xs hidden" id="loginEmailValidation"></div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300" for="loginPassword">Senha</label>
<input class="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-300" id="loginPassword" name="loginPassword" required="" type="password">
</div>
<button class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-300" type="submit">
            Entrar
          </button>
<p class="text-xs text-center text-gray-500 dark:text-gray-400">
            Não tem conta? <button class="text-sky-600 dark:text-sky-400 hover:underline font-medium" id="showSignUp" type="button">Cadastre-se</button>
</p>
<p class="text-xs text-center text-gray-500 dark:text-gray-400 mt-2">
<button class="text-sky-600 dark:text-sky-400 hover:underline font-medium" id="forgotPassword" type="button">Esqueci minha senha</button>
</p>
<button class="w-full mt-2 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-semibold py-2 px-4 rounded-lg shadow-sm transition-all duration-300" id="backToMainFromLogin" type="button">
            Voltar
          </button>
</form>
<!-- Formulário de Cadastro -->
<form class="space-y-4 mb-4 hidden" id="signUpForm">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">Cadastro</h3>
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300" for="signUpEmail">Email</label>
<input class="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-300" id="signUpEmail" name="signUpEmail" required="" type="email">
<div class="mt-1 text-xs hidden" id="signUpEmailValidation"></div>
</div>
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300" for="signUpPassword">Senha (mínimo 6 caracteres)</label>
<input class="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-300" id="signUpPassword" name="signUpPassword" required="" type="password">
</div>
<button class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-300" type="submit">
            Cadastrar
          </button>
<p class="text-xs text-center text-gray-500 dark:text-gray-400">
            Já tem conta? <button class="text-sky-600 dark:text-sky-400 hover:underline font-medium" id="showLogin" type="button">Faça Login</button>
</p>
<button class="w-full mt-2 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-semibold py-2 px-4 rounded-lg shadow-sm transition-all duration-300" id="backToMainFromSignUp" type="button">
            Voltar
          </button>
</form>
</div>
<!-- Botões de Ação -->
<div class="space-y-3" id="authActions">
<button class="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center space-x-2 transform hover:-translate-y-0.5 transition-all duration-300" id="loginWithEmailButton">
<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
<path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
<path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
</svg>
<span>Entrar com Email</span>
</button>
<button class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center space-x-2 transform hover:-translate-y-0.5 transition-all duration-300" id="googleSignInButton">
<svg class="h-5 w-5" height="24px" viewBox="0 0 48 48" width="24px" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
<path d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" fill="#FFC107"></path>
<path d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" fill="#FF3D00"></path>
<path d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" fill="#4CAF50"></path>
<path d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" fill="#1976D2"></path>
</svg>
<span>Entrar com Google</span>
</button>
</div>
</section>
<!-- Informações do Usuário -->
<div class="hidden text-center mb-4 p-3 bg-sky-50 dark:bg-sky-900/20 rounded-lg" id="userInfo">
<p class="text-gray-700 dark:text-gray-200">
        Logado como: <span class="font-semibold" id="userEmailDisplay"></span>
</p>
<div class="mt-2 space-x-4">
<button class="text-sm text-blue-500 dark:text-blue-400 hover:underline font-medium" id="showHistoryButton">
          📊 Meu Histórico
        </button>
<button class="text-sm text-red-500 dark:text-red-400 hover:underline font-medium" id="logoutButton">
          🚪 Logout
        </button>
</div>
</div>
<!-- Seção Principal -->
<main id="mainContentSection" class="">
<div class="space-y-4">
<!-- Campo URL -->
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="longUrl">
            Cole sua URL longa aqui:
          </label>
<div class="relative">
<input class="w-full px-4 py-3 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-300" id="longUrl" name="longUrl" placeholder="https://www.seusite.com" type="url"><div class="mt-1 text-xs hidden" id="urlValidation"></div>
<div class="absolute inset-y-0 right-0 flex items-center pr-3">
<svg class="h-5 w-5 text-gray-400 hidden" fill="none" id="urlValidIcon" stroke="currentColor" viewBox="0 0 24 24">
<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</div>
</div>
<div class="mt-1 text-xs hidden" id="urlValidation"></div>
</div>
<!-- Campo Código Personalizado -->
<div class="hidden" id="customShortCodeSection">
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="customShortCode">
            Código Curto Personalizado (opcional):
          </label>
<div class="flex items-center">
<span class="px-3 py-3 border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-l-lg">
              encurt.ar/
            </span>
<input class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-r-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-300" id="customShortCode" name="customShortCode" placeholder="ex: minhamarca" type="text">
</div>
<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Apenas letras minúsculas, números e hífens. Máx. 20 caracteres.
          </p>
</div>
<!-- Opções Avançadas (para usuários logados) -->
<div class="hidden space-y-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg" id="advancedOptions">
<h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">⚙️ Opções Avançadas</h4>
<!-- Expiração -->
<div class="flex items-center space-x-3">
<input class="rounded text-sky-500 focus:ring-sky-500" id="enableExpiration" type="checkbox">
<label class="text-sm text-gray-700 dark:text-gray-300" for="enableExpiration">
              Link com expiração
            </label>
<input class="hidden text-sm px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600" id="expirationDate" type="datetime-local">
</div>
<!-- Proteção com senha -->
<div class="flex items-center space-x-3">
<input class="rounded text-sky-500 focus:ring-sky-500" id="enablePassword" type="checkbox">
<label class="text-sm text-gray-700 dark:text-gray-300" for="enablePassword">
              Proteger com senha
            </label>
<input class="hidden text-sm px-2 py-1 border rounded dark:bg-gray-700 dark:border-gray-600" id="linkPassword" placeholder="Senha" type="password">
</div>
</div>
<!-- Botão Encurtar -->
<button class="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transform hover:-translate-y-0.5 transition-all duration-300 flex items-center justify-center space-x-2" id="shortenButton">
<span>Encurtar Agora</span>
<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
<path clip-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" fill-rule="evenodd"></path>
</svg>
</button>
<!-- Bulk URL Shortening -->
<button class="hidden w-full text-sm text-sky-600 dark:text-sky-400 hover:underline" id="bulkShortenerToggle">
          📋 Encurtar múltiplas URLs
        </button>
<!-- Loading -->
<div class="hidden flex justify-center items-center py-2" id="loadingIndicator">
<div class="loader-dots text-sky-500">
<div></div>
<div></div>
<div></div>
</div>
<p class="ml-3 text-gray-600 dark:text-gray-400">Processando...</p>
</div>
</div>
<!-- Resultado -->
<div class="mt-6 bg-gradient-to-r from-sky-50 to-indigo-50 dark:from-sky-900/20 dark:to-indigo-900/20 p-4 rounded-lg shadow-inner hidden animate-slide-up" id="result">
<p class="text-sm text-gray-700 dark:text-gray-300 mb-2">Sua URL encurtada:</p>
<div class="flex items-center space-x-2 mb-3">
<a class="text-sky-600 dark:text-sky-400 font-semibold text-lg break-all flex-grow hover:underline cursor-pointer" id="shortUrlText" rel="noopener noreferrer" target="_blank"></a>
<button class="p-2 rounded-lg bg-sky-500 hover:bg-sky-600 text-white focus:outline-none focus:ring-2 focus:ring-sky-400 transition-all duration-300 tooltip" id="copyButton" title="Copiar URL" aria-label="Copiar URL encurtada para a área de transferência">
<svg class="h-5 w-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
<path d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>
<span class="tooltip-text">Copiar</span>
</button>
</div>
<!-- QR Code -->
<div class="flex justify-center mb-3">
<div class="p-3 bg-white dark:bg-gray-800 rounded-lg shadow-md" id="qrcode"></div>
</div>
<!-- Botões de ação -->
<div class="flex gap-2">
<button class="flex-1 text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-3 rounded-lg transition-all duration-300" id="downloadQRButton">
            📥 Download QR
          </button>
<button class="flex-1 text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-3 rounded-lg transition-all duration-300" id="previewButton">
            👁️ Preview
          </button>
</div>
</div>
</main>
<!-- Seção de Histórico -->
<section class="hidden mt-8" id="historySection">
<div class="glass rounded-xl shadow-lg p-6">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">📊 Meu Histórico de URLs</h3>
<div class="flex gap-2">
<button class="text-sm bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-lg transition-all duration-300 tooltip" id="exportHistoryButton">
              📥 Exportar CSV
              <span class="tooltip-text">Exportar dados</span>
</button>
<button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200" id="hideHistoryButton" aria-label="Fechar seção de histórico">
              ✕
            </button>
</div>
</div>
<!-- Estatísticas Gerais -->
<div class="grid grid-cols-3 gap-3 mb-4" id="statsOverview">
<div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg text-center">
<div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="totalUrlsCount">0</div>
<div class="text-xs text-gray-600 dark:text-gray-400">URLs Totais</div>
</div>
<div class="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg text-center">
<div class="text-2xl font-bold text-green-600 dark:text-green-400" id="totalClicksCount">0</div>
<div class="text-xs text-gray-600 dark:text-gray-400">Cliques Totais</div>
</div>
<div class="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg text-center">
<div class="text-2xl font-bold text-purple-600 dark:text-purple-400" id="avgClicksCount">0</div>
<div class="text-xs text-gray-600 dark:text-gray-400">Média de Cliques</div>
</div>
</div>
<!-- Gráfico de Analytics -->
<div class="mb-4 hidden" id="analyticsChart">
<h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">📈 Cliques nos últimos 7 dias</h4>
<div class="chart-container">
<canvas id="clicksChart"></canvas>
</div>
</div>
<!-- Lista de URLs -->
<div class="space-y-3 max-h-96 overflow-y-auto" id="historyContent">
<div class="text-center py-4 text-gray-500 dark:text-gray-400" id="historyLoading">
<div class="loader-dots text-sky-500 justify-center">
<div></div>
<div></div>
<div></div>
</div>
<p class="mt-2">Carregando histórico...</p>
</div>
<div class="hidden text-center py-4 text-gray-500 dark:text-gray-400" id="historyEmpty">
            Você ainda não criou nenhuma URL encurtada.
          </div>
<div class="space-y-3" id="historyList"></div>
</div>
</div>
</section>
<!-- Footer -->
<footer class="text-center mt-8 text-xs text-gray-600 dark:text-gray-400">
<div class="mb-3 space-y-2">
<div class="flex flex-wrap justify-center gap-4 text-xs">
<a class="text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 hover:underline transition-colors" href="https://encurt.ar/about.html">Sobre</a>
<a class="text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 hover:underline transition-colors" href="https://encurt.ar/contact.html">Contato</a>
<a class="text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 hover:underline transition-colors" href="https://encurt.ar/privacy.html">Privacidade</a>
<a class="text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 hover:underline transition-colors" href="https://encurt.ar/terms.html">Termos de Uso</a>
</div>
<div class="text-xs text-gray-500 dark:text-gray-500">
<p>Hospedado com ❤️ no Google Firebase</p>
</div>
</div>
<p>© <span id="currentYear">2025</span> Encurt.ar - Todos os direitos reservados.</p>
<p class="mt-1 font-mono text-gray-500 dark:text-gray-500" id="authStatusDisplay">ID Anônimo: U61Ksb7P... (Recurso de código personalizado desabilitado)</p>
</footer>
</div>
<!-- Modal de Recuperação de Senha -->
<div class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in" id="forgotPasswordModal">
<div class="glass rounded-xl shadow-2xl p-6 w-full max-w-md mx-4 animate-slide-up">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">🔑 Recuperar Senha</h3>
<button class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-xl transition-colors" id="closeForgotPasswordModal" aria-label="Fechar modal de recuperação de senha">✕</button>
</div>
<form class="space-y-4" id="forgotPasswordForm">
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300" for="forgotPasswordEmail">Email</label>
<input class="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-all duration-300" id="forgotPasswordEmail" name="forgotPasswordEmail" placeholder="Digite seu email" required="" type="email">
<div class="mt-1 text-xs hidden" id="forgotPasswordEmailValidation"></div>
</div>
<button class="w-full bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-300" type="submit">
          Enviar Link de Recuperação
        </button>
</form>
</div>
</div>
<!-- Modal de Preview -->
<div class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in" id="previewModal">
<div class="glass rounded-xl shadow-2xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden animate-slide-up">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">👁️ Preview do Link</h3>
<button class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-xl transition-colors" id="closePreviewModal" aria-label="Fechar modal de preview">✕</button>
</div>
<div class="space-y-4" id="previewContent">
<div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
<p class="text-sm text-gray-600 dark:text-gray-400 mb-1">URL Original:</p>
<p class="text-base font-medium text-gray-800 dark:text-gray-200 break-all" id="previewUrl"></p>
</div>
<div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
<p class="text-sm text-yellow-800 dark:text-yellow-200">
            ⚠️ Certifique-se de que este é o destino correto antes de prosseguir.
          </p>
</div>
<!-- Loading indicator para o iframe -->
<div class="hidden text-center py-8" id="previewLoading">
<div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-sky-500"></div>
<p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Carregando preview...</p>
</div>
<!-- Container do iframe -->
<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden" id="previewFrame" style="height: 400px;">
<iframe id="sitePreview" class="w-full h-full border-0" sandbox="allow-scripts allow-same-origin allow-popups allow-forms" loading="lazy" title="Preview do site" src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/saved_resource.html">
</iframe>
</div>
<!-- Mensagem de erro caso o iframe não carregue -->
<div class="hidden bg-red-50 dark:bg-red-900/20 p-4 rounded-lg" id="previewError">
<p class="text-sm text-red-800 dark:text-red-200">
            ❌ Não foi possível carregar a prévia do site. Alguns sites não permitem ser exibidos em frames por questões de segurança.
          </p>
</div>
<div class="flex gap-3">
<button class="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300" id="proceedToUrl">
            Prosseguir para o site
          </button>
<button class="flex-1 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-semibold py-2 px-4 rounded-lg transition-all duration-300" id="cancelPreview">
            Cancelar
          </button>
</div>
</div>
</div>
</div>
<!-- Modal de Bulk URL -->
<div class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in" id="bulkModal">
<div class="glass rounded-xl shadow-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto animate-slide-up">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">📋 Encurtar Múltiplas URLs</h3>
<button class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-xl transition-colors" id="closeBulkModal" aria-label="Fechar modal de múltiplas URLs">✕</button>
</div>
<div class="space-y-4">
<div>
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Cole suas URLs (uma por linha):
          </label>
<textarea class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500" id="bulkUrls" placeholder="https://exemplo1.com
https://exemplo2.com
https://exemplo3.com" rows="6"></textarea>
</div>
<button class="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300" id="processBulkUrls">
          Processar URLs
        </button>
<div class="hidden space-y-2 max-h-64 overflow-y-auto" id="bulkResults"></div>
</div>
</div>
</div>
<!-- Scripts -->
<script src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/script.js.download" type="module"></script>
<!-- Service Worker para PWA -->
<script>
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').then(function(registration) {
        console.log('Service Worker registrado com sucesso:', registration.scope);
      }).catch(function(err) {
        console.log('Falha ao registrar Service Worker:', err);
      });
    }
  </script>
<!-- Google Analytics -->
<script async="" src="./encurt.ar - Encurtador de Links Gratuito e Rápido_files/js"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-L12DSJ4R7D');
  </script>

</body></html>