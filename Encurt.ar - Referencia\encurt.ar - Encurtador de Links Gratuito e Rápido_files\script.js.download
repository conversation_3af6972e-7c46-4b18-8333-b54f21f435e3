// Importações do Firebase
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
import {
    getAuth,
    signInAnonymously,
    onAuthStateChanged,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    GoogleAuthProvider,
    signInWithPopup,
    signOut,
    sendPasswordResetEmail
} from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
import { getFirestore, doc, setDoc, getDoc, serverTimestamp, collection, query, where, orderBy, getDocs, deleteDoc } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

// --- Configuração do Firebase ---
// Prioriza a configuração do ambiente Canvas (__firebase_config)
// Se não disponível, usa a configuração hardcoded do seu script original.
let activeFirebaseConfig;
const firebaseConfigJSONFromEnv = typeof __firebase_config !== 'undefined' ? __firebase_config : null;

const localHardcodedConfig = { // Sua configuração original como fallback
    apiKey: "AIzaSyC7QSYVWJsKX0UxhkK8zthnzcwH3A9AgbY",
    authDomain: "encurtar-app.firebaseapp.com",
    projectId: "encurtar-app",
    storageBucket: "encurtar-app.firebasestorage.app",
    messagingSenderId: "730701938603",
    appId: "1:730701938603:web:004b3b96540402cb521cee",
    measurementId: "G-L12DSJ4R7D"
};

// Elementos da UI (definidos cedo para que displayMessage possa usá-los)
const messageContainer = document.getElementById('messageContainer');

if (firebaseConfigJSONFromEnv) {
    try {
        activeFirebaseConfig = JSON.parse(firebaseConfigJSONFromEnv);
        console.log("Usando configuração Firebase do ambiente Canvas.");
        if (!activeFirebaseConfig.apiKey) {
             console.warn("API Key ausente na configuração do Firebase do ambiente. Autenticação falhará.");
             if (messageContainer) displayMessage("API Key do Firebase ausente. Autenticação não funcionará.", true);
        }
    } catch (parseError) {
        console.error("Erro ao parsear __firebase_config, usando configuração local hardcoded:", parseError);
        activeFirebaseConfig = localHardcodedConfig;
        if (messageContainer) displayMessage("Erro na configuração do Firebase do ambiente, usando config local.", true);
    }
} else {
    console.log("Variável __firebase_config não encontrada. Usando configuração local hardcoded.");
    activeFirebaseConfig = localHardcodedConfig;
}

// --- Inicialização do Firebase ---
let app, db, auth, googleProvider;
try {
    app = initializeApp(activeFirebaseConfig);
    db = getFirestore(app);
    auth = getAuth(app);
    googleProvider = new GoogleAuthProvider();
    console.log("Firebase SDK inicializado.");
} catch (e) {
    console.error("Erro crítico ao inicializar Firebase SDK: ", e.message, e.stack);
    if (messageContainer) displayMessage(`Erro fatal ao inicializar Firebase: ${e.message}. A aplicação pode não funcionar. Verifique a configuração.`, true);
}

// ID da aplicação (usado no caminho do Firestore)
const firestoreAppId = activeFirebaseConfig.appId || "ID_APP_NAO_CONFIGURADO_VERIFICAR"; 
let currentUserId = null; 
let isUserAnonymous = true;

// Elementos da UI (restantes)
const longUrlInput = document.getElementById('longUrl');
const shortenButton = document.getElementById('shortenButton');
const resultDiv = document.getElementById('result');
const shortUrlText = document.getElementById('shortUrlText');
const copyButton = document.getElementById('copyButton');
const loadingIndicator = document.getElementById('loadingIndicator');
const customShortCodeInput = document.getElementById('customShortCode');
const customShortCodeSection = document.getElementById('customShortCodeSection');
const mainContentSection = document.getElementById('mainContentSection');

// Elementos de Autenticação
const authSection = document.getElementById('authSection');
const authForms = document.getElementById('authForms');
const authActions = document.getElementById('authActions');
const loginForm = document.getElementById('loginForm');
const signUpForm = document.getElementById('signUpForm');
const loginEmailInput = document.getElementById('loginEmail');
const loginPasswordInput = document.getElementById('loginPassword');
const signUpEmailInput = document.getElementById('signUpEmail');
const signUpPasswordInput = document.getElementById('signUpPassword');
const googleSignInButton = document.getElementById('googleSignInButton');
const loginWithEmailButton = document.getElementById('loginWithEmailButton');
const showSignUpButton = document.getElementById('showSignUp');
const showLoginButton = document.getElementById('showLogin');
const backToMainFromLoginButton = document.getElementById('backToMainFromLogin');
const backToMainFromSignUpButton = document.getElementById('backToMainFromSignUp');

// Elementos de validação de email
const loginEmailValidation = document.getElementById('loginEmailValidation');
const signUpEmailValidation = document.getElementById('signUpEmailValidation');

// Elementos de recuperação de senha
const forgotPasswordButton = document.getElementById('forgotPassword');
const forgotPasswordModal = document.getElementById('forgotPasswordModal');
const closeForgotPasswordModal = document.getElementById('closeForgotPasswordModal');
const forgotPasswordForm = document.getElementById('forgotPasswordForm');
const forgotPasswordEmailInput = document.getElementById('forgotPasswordEmail');
const forgotPasswordEmailValidation = document.getElementById('forgotPasswordEmailValidation');

// Elementos de histórico
const showHistoryButton = document.getElementById('showHistoryButton');
const hideHistoryButton = document.getElementById('hideHistoryButton');
const historySection = document.getElementById('historySection');
const historyLoading = document.getElementById('historyLoading');
const historyEmpty = document.getElementById('historyEmpty');
const historyList = document.getElementById('historyList');

const userInfoDiv = document.getElementById('userInfo');
const userEmailDisplay = document.getElementById('userEmailDisplay');
const logoutButton = document.getElementById('logoutButton');
const authStatusDisplay = document.getElementById('authStatusDisplay'); // Elemento do rodapé para status

// Função para exibir mensagens
function displayMessage(message, isError = false) {
    if (!messageContainer) {
        console.warn("Message container não encontrado ao tentar exibir: ", message);
        return;
    }
    messageContainer.innerHTML = '';
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.className = `p-3 rounded-md text-sm ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`;
    messageContainer.appendChild(messageDiv);
    setTimeout(() => {
        if (messageContainer) messageContainer.innerHTML = '';
    }, 5000);
}

// Função para validar email em tempo real
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Função para validar URL de forma mais robusta
function validateUrl(url) {
    // Remove espaços em branco
    url = url.trim();

    // Verifica se não está vazio
    if (!url) {
        return false;
    }

    // Adiciona protocolo se não tiver
    let testUrl = url;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
        testUrl = 'https://' + testUrl;
    }

    try {
        // Usa a API nativa do navegador para validar
        const urlObj = new URL(testUrl);

        // Verifica se tem hostname válido
        if (!urlObj.hostname || urlObj.hostname.length < 3) {
            return false;
        }

        // Verifica se tem pelo menos um ponto no hostname (domínio.tld)
        if (!urlObj.hostname.includes('.')) {
            return false;
        }

        // Verifica se o hostname não começa ou termina com ponto
        if (urlObj.hostname.startsWith('.') || urlObj.hostname.endsWith('.')) {
            return false;
        }

        // Verifica se não tem pontos consecutivos
        if (urlObj.hostname.includes('..')) {
            return false;
        }

        // Divide o hostname em partes
        const parts = urlObj.hostname.split('.');

        // Deve ter pelo menos 2 partes (domínio.tld)
        if (parts.length < 2) {
            return false;
        }

        // Verifica cada parte do domínio
        for (const part of parts) {
            // Cada parte deve ter pelo menos 1 caractere
            if (part.length === 0) {
                return false;
            }

            // Cada parte deve começar e terminar com alfanumérico
            if (!/^[a-zA-Z0-9]/.test(part) || !/[a-zA-Z0-9]$/.test(part)) {
                return false;
            }

            // Cada parte pode conter apenas letras, números e hífens
            if (!/^[a-zA-Z0-9-]+$/.test(part)) {
                return false;
            }
        }

        // A última parte (TLD) deve ter pelo menos 2 caracteres e ser apenas letras
        const tld = parts[parts.length - 1];
        if (tld.length < 2 || !/^[a-zA-Z]+$/.test(tld)) {
            return false;
        }

        return true;
    } catch (e) {
        return false;
    }
}

function showEmailValidation(element, isValid, message) {
    if (!element) return;

    element.classList.remove('hidden');
    if (isValid) {
        element.className = 'mt-1 text-xs text-green-600';
        element.textContent = '✓ Email válido';
    } else {
        element.className = 'mt-1 text-xs text-red-600';
        element.textContent = message || '✗ Formato de email inválido';
    }
}

function hideEmailValidation(element) {
    if (element) {
        element.classList.add('hidden');
    }
}

// Atualiza a UI com base no estado de autenticação
function updateUIForAuthState(user) {
    if (user && !user.isAnonymous) {
        isUserAnonymous = false;
        currentUserId = user.uid;
        if (authSection) authSection.classList.add('hidden');
        if (authForms) authForms.classList.add('hidden');
        if (userInfoDiv) userInfoDiv.classList.remove('hidden');
        if (userEmailDisplay) userEmailDisplay.textContent = user.email || user.displayName || 'Usuário Logado';
        if (authStatusDisplay) authStatusDisplay.textContent = `Logado como: ${user.email || user.displayName || user.uid.substring(0,8) + "..."}`;
        if (customShortCodeSection) customShortCodeSection.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    } else {
        isUserAnonymous = true;
        if (user) { // Usuário anônimo
             currentUserId = user.uid;
             if (authStatusDisplay) authStatusDisplay.textContent = `ID Anônimo: ${user.uid.substring(0,8)}... (Recurso de código personalizado desabilitado)`;
        } else { // Usuário deslogado (null)
            currentUserId = null;
            if (authStatusDisplay) authStatusDisplay.textContent = "Você não está logado. Faça login para usar códigos personalizados.";
        }

        if (authSection) authSection.classList.remove('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (authForms) authForms.classList.add('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        if (signUpForm) signUpForm.classList.add('hidden');
        if (userInfoDiv) userInfoDiv.classList.add('hidden');
        if (customShortCodeSection) customShortCodeSection.classList.add('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    }
}

// Observador de estado de autenticação
if (auth) {
    onAuthStateChanged(auth, async (user) => {
        if (user) {
            updateUIForAuthState(user);
        } else {
            try {
                const userCredential = await signInAnonymously(auth);
                console.log("Usuário anônimo para funcionalidades básicas:", userCredential.user.uid);
                updateUIForAuthState(userCredential.user);
            } catch (error) {
                console.error("Erro na autenticação anônima:", error);
                displayMessage("Erro de autenticação. Verifique se a API Key do Firebase é válida.", true);
                updateUIForAuthState(null);
            }
        }
    });
} else {
    console.error("Firebase Auth não foi inicializado. A autenticação não funcionará.");
    updateUIForAuthState(null);
    displayMessage("Erro na configuração do Firebase. A autenticação está desabilitada.", true);
}

// --- Validação de URL em Tempo Real ---
if (longUrlInput) {
    // Criar elemento de validação para URL
    const urlValidation = document.createElement('div');
    urlValidation.className = 'mt-1 text-xs hidden';
    urlValidation.id = 'urlValidation';
    longUrlInput.parentNode.insertBefore(urlValidation, longUrlInput.nextSibling);

    longUrlInput.addEventListener('input', (e) => {
        const url = e.target.value.trim();
        if (url.length === 0) {
            urlValidation.classList.add('hidden');
        } else if (validateUrl(url)) {
            urlValidation.className = 'mt-1 text-xs text-green-600';
            urlValidation.textContent = '✓ URL válida';
            urlValidation.classList.remove('hidden');
        } else {
            urlValidation.className = 'mt-1 text-xs text-red-600';
            urlValidation.textContent = '✗ Insira uma URL válida (ex: https://www.google.com)';
            urlValidation.classList.remove('hidden');
        }
    });
}

// --- Validação de Email em Tempo Real ---
if (loginEmailInput) {
    loginEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(loginEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(loginEmailValidation, true);
        } else {
            showEmailValidation(loginEmailValidation, false);
        }
    });
}

if (signUpEmailInput) {
    signUpEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(signUpEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(signUpEmailValidation, true);
        } else {
            showEmailValidation(signUpEmailValidation, false);
        }
    });
}

if (forgotPasswordEmailInput) {
    forgotPasswordEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(forgotPasswordEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(forgotPasswordEmailValidation, true);
        } else {
            showEmailValidation(forgotPasswordEmailValidation, false);
        }
    });
}

// --- Manipuladores de Eventos de Autenticação ---
if (loginWithEmailButton) {
    loginWithEmailButton.addEventListener('click', () => {
        if (authActions) authActions.classList.add('hidden');
        if (authForms) authForms.classList.remove('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        if (signUpForm) signUpForm.classList.add('hidden');
        if (mainContentSection) mainContentSection.classList.add('hidden');
    });
}

if (showSignUpButton) {
    showSignUpButton.addEventListener('click', (e) => {
        e.preventDefault();
        if (loginForm) loginForm.classList.add('hidden');
        if (signUpForm) signUpForm.classList.remove('hidden');
        // Mantém a seção principal oculta durante o cadastro
    });
}

if (showLoginButton) {
    showLoginButton.addEventListener('click', (e) => {
        e.preventDefault();
        if (signUpForm) signUpForm.classList.add('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        // Mantém a seção principal oculta durante o login
    });
}

if (backToMainFromLoginButton) {
    backToMainFromLoginButton.addEventListener('click', () => {
        if (authForms) authForms.classList.add('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    });
}

if (backToMainFromSignUpButton) {
    backToMainFromSignUpButton.addEventListener('click', () => {
        if (authForms) authForms.classList.add('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    });
}

// --- Recuperação de Senha ---
if (forgotPasswordButton) {
    forgotPasswordButton.addEventListener('click', () => {
        if (forgotPasswordModal) forgotPasswordModal.classList.remove('hidden');
    });
}

if (closeForgotPasswordModal) {
    closeForgotPasswordModal.addEventListener('click', () => {
        if (forgotPasswordModal) forgotPasswordModal.classList.add('hidden');
        if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
        hideEmailValidation(forgotPasswordEmailValidation);
    });
}

// Fechar modal clicando fora dele
if (forgotPasswordModal) {
    forgotPasswordModal.addEventListener('click', (e) => {
        if (e.target === forgotPasswordModal) {
            forgotPasswordModal.classList.add('hidden');
            if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
            hideEmailValidation(forgotPasswordEmailValidation);
        }
    });
}

if (forgotPasswordForm) {
    forgotPasswordForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) {
            displayMessage("Firebase Auth não inicializado.", true);
            return;
        }

        const email = forgotPasswordEmailInput.value.trim();
        if (!email) {
            displayMessage("Por favor, digite seu email.", true);
            return;
        }

        if (!validateEmail(email)) {
            displayMessage("Por favor, digite um email válido.", true);
            return;
        }

        try {
            await sendPasswordResetEmail(auth, email);
            displayMessage("Link de recuperação enviado para seu email!", false);
            if (forgotPasswordModal) forgotPasswordModal.classList.add('hidden');
            if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
            hideEmailValidation(forgotPasswordEmailValidation);
        } catch (error) {
            console.error("Erro ao enviar email de recuperação:", error);
            let errorMessage = "Erro ao enviar email de recuperação.";

            if (error.code === 'auth/user-not-found') {
                errorMessage = "Nenhuma conta encontrada com este email.";
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = "Email inválido.";
            } else if (error.code === 'auth/too-many-requests') {
                errorMessage = "Muitas tentativas. Tente novamente mais tarde.";
            }

            displayMessage(errorMessage, true);
        }
    });
}

if (loginForm) {
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }
        const email = loginEmailInput.value;
        const password = loginPasswordInput.value;
        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        try {
            await signInWithEmailAndPassword(auth, email, password);
            displayMessage("Login bem-sucedido!", false);
        } catch (error) {
            console.error("Erro no login:", error);
            let errorMessage = `Erro no login: ${error.message}`;

            // Tratamento específico para erro de operação não permitida
            if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Erro no login: Firebase: Error (auth/operation-not-allowed). " +
                              "O login por e-mail/senha não está habilitado no projeto Firebase. " +
                              "Para resolver: 1) Acesse o Firebase Console, 2) Vá em Authentication > Sign-in method, " +
                              "3) Habilite 'Email/Password' como provedor de login.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
        }
    });
}

if (signUpForm) {
    signUpForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }
        const email = signUpEmailInput.value;
        const password = signUpPasswordInput.value;
        if (password.length < 6) {
            displayMessage("A senha deve ter pelo menos 6 caracteres.", true);
            return;
        }
        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        try {
            await createUserWithEmailAndPassword(auth, email, password);
            displayMessage("Cadastro bem-sucedido! Você está logado.", false);
        } catch (error) {
            console.error("Erro no cadastro:", error);
            let errorMessage = `Erro no cadastro: ${error.message}`;

            // Tratamento específico para erro de operação não permitida
            if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Erro no cadastro: Firebase: Error (auth/operation-not-allowed). " +
                              "O cadastro por e-mail/senha não está habilitado no projeto Firebase. " +
                              "Para resolver: 1) Acesse o Firebase Console, 2) Vá em Authentication > Sign-in method, " +
                              "3) Habilite 'Email/Password' como provedor de login.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
        }
    });
}

if (googleSignInButton) {
    googleSignInButton.addEventListener('click', async () => {
        if (!auth || !googleProvider) { displayMessage("Firebase Auth não inicializado.", true); return; }
        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        try {
            await signInWithPopup(auth, googleProvider);
            displayMessage("Login com Google bem-sucedido!", false);
        } catch (error) {
            console.error("Erro no login com Google:", error);
            let errorMessage = "Erro ao fazer login com Google.";

            // Tratamento específico para diferentes tipos de erro do Google
            if (error.code === 'auth/popup-closed-by-user') {
                errorMessage = "Login cancelado. A janela do Google foi fechada.";
            } else if (error.code === 'auth/popup-blocked') {
                errorMessage = "Pop-up bloqueado pelo navegador. Permita pop-ups para este site.";
            } else if (error.code === 'auth/cancelled-popup-request') {
                errorMessage = "Solicitação de login cancelada. Tente novamente.";
            } else if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Login com Google não está habilitado. Entre em contato com o suporte.";
            } else if (error.code === 'auth/unauthorized-domain') {
                errorMessage = "Domínio não autorizado para login com Google.";
            } else if (error.code === 'auth/account-exists-with-different-credential') {
                errorMessage = "Já existe uma conta com este email usando outro método de login.";
            } else if (error.code === 'auth/network-request-failed') {
                errorMessage = "Erro de conexão. Verifique sua internet e tente novamente.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
        }
    });
}

if (logoutButton) {
    logoutButton.addEventListener('click', async () => {
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }
        try {
            await signOut(auth);
            displayMessage("Logout bem-sucedido.", false);
        } catch (error) {
            console.error("Erro no logout:", error);
            displayMessage("Erro ao fazer logout.", true);
        }
    });
}

// --- Histórico de URLs ---
if (showHistoryButton) {
    showHistoryButton.addEventListener('click', async () => {
        if (historySection) historySection.classList.remove('hidden');
        await loadUserHistory();
    });
}

if (hideHistoryButton) {
    hideHistoryButton.addEventListener('click', () => {
        if (historySection) historySection.classList.add('hidden');
    });
}

// --- Funções de Histórico ---
async function loadUserHistory() {
    console.log("loadUserHistory chamada - currentUserId:", currentUserId, "isUserAnonymous:", isUserAnonymous);

    if (!auth || !db) {
        console.error("Firebase não inicializado");
        if (historyEmpty) {
            historyEmpty.textContent = "Erro de configuração. Firebase não inicializado.";
            historyEmpty.classList.remove('hidden');
        }
        if (historyLoading) historyLoading.classList.add('hidden');
        return;
    }

    if (!currentUserId || isUserAnonymous) {
        console.log("Usuário não logado ou anônimo");
        if (historyEmpty) {
            historyEmpty.textContent = "Faça login para ver seu histórico de URLs.";
            historyEmpty.classList.remove('hidden');
        }
        if (historyLoading) historyLoading.classList.add('hidden');
        return;
    }

    try {
        if (historyLoading) historyLoading.classList.remove('hidden');
        if (historyEmpty) historyEmpty.classList.add('hidden');
        if (historyList) historyList.innerHTML = '';

        // Query para buscar URLs do usuário
        const linksCollection = collection(db, "artifacts", firestoreAppId, "public", "data", "links");
        const userLinksQuery = query(
            linksCollection,
            where("creatorUserId", "==", currentUserId),
            orderBy("createdAt", "desc")
        );

        const querySnapshot = await getDocs(userLinksQuery);

        if (historyLoading) historyLoading.classList.add('hidden');

        if (querySnapshot.empty) {
            if (historyEmpty) historyEmpty.classList.remove('hidden');
            return;
        }

        const urls = [];
        querySnapshot.forEach((doc) => {
            urls.push({ id: doc.id, ...doc.data() });
        });

        displayUserHistory(urls);

    } catch (error) {
        console.error("Erro ao carregar histórico:", error);
        console.error("Detalhes do erro:", error.message, error.code);
        if (historyLoading) historyLoading.classList.add('hidden');
        if (historyEmpty) {
            let errorMessage = "Erro ao carregar histórico. Tente novamente.";

            // Tratamento específico para diferentes tipos de erro
            if (error.code === 'failed-precondition') {
                errorMessage = "Índice do banco de dados não configurado. Criando URLs primeiro...";
            } else if (error.code === 'permission-denied') {
                errorMessage = "Permissão negada. Faça login novamente.";
            } else if (error.code === 'unavailable') {
                errorMessage = "Serviço temporariamente indisponível. Tente em alguns minutos.";
            }

            historyEmpty.textContent = errorMessage;
            historyEmpty.classList.remove('hidden');
        }

        // Exibe mensagem de erro também na interface principal
        displayMessage("Erro ao carregar histórico. Verifique o console para detalhes.", true);
    }
}

function displayUserHistory(urls) {
    if (!historyList) return;

    historyList.innerHTML = '';

    urls.forEach((urlData) => {
        const urlItem = createHistoryItem(urlData);
        historyList.appendChild(urlItem);
    });
}

function createHistoryItem(urlData) {
    const item = document.createElement('div');
    item.className = 'bg-gray-50 rounded-lg p-4 border border-gray-200';

    const createdDate = urlData.createdAt ?
        new Date(urlData.createdAt.seconds * 1000).toLocaleDateString('pt-BR') :
        'Data não disponível';

    const lastAccessedDate = urlData.lastAccessed ?
        new Date(urlData.lastAccessed.seconds * 1000).toLocaleDateString('pt-BR') :
        'Nunca acessado';

    const clickCount = urlData.clickCount || 0;

    item.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                    encurt.ar/${urlData.shortCode}
                </p>
                <p class="text-xs text-gray-500 truncate" title="${urlData.longUrl}">
                    ${urlData.longUrl}
                </p>
                <div class="flex flex-wrap gap-2 mt-1 text-xs text-gray-400">
                    <span>Criado: ${createdDate}</span>
                    <span>•</span>
                    <span>Cliques: ${clickCount}</span>
                    <span>•</span>
                    <span>Último acesso: ${lastAccessedDate}</span>
                    ${urlData.isCustom ? '<span>• Código personalizado</span>' : ''}
                </div>
            </div>
            <div class="flex space-x-2 ml-4">
                <button onclick="copyUrlFromHistory('${urlData.shortCode}')"
                        class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                    Copiar
                </button>
                <button onclick="deleteUrlFromHistory('${urlData.id}', '${urlData.shortCode}')"
                        class="text-red-500 hover:text-red-700 text-sm font-medium">
                    Excluir
                </button>
            </div>
        </div>
    `;

    return item;
}

// Funções globais para os botões do histórico
window.copyUrlFromHistory = function(shortCode) {
    const fullUrl = `https://encurt.ar/${shortCode}`;

    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(fullUrl).then(() => {
            displayMessage("URL copiada para a área de transferência!", false);
        }).catch(err => {
            console.error('Erro ao copiar:', err);
            legacyCopy(fullUrl);
        });
    } else {
        legacyCopy(fullUrl);
    }
};

window.deleteUrlFromHistory = async function(docId, shortCode) {
    if (!confirm(`Tem certeza que deseja excluir a URL encurt.ar/${shortCode}?`)) {
        return;
    }

    try {
        const docRef = doc(db, "artifacts", firestoreAppId, "public", "data", "links", docId);
        await deleteDoc(docRef);
        displayMessage("URL excluída com sucesso!", false);
        await loadUserHistory(); // Recarrega o histórico
    } catch (error) {
        console.error("Erro ao excluir URL:", error);
        displayMessage("Erro ao excluir URL. Tente novamente.", true);
    }
};

// --- Lógica de Encurtamento de Link ---
function generateShortCode(length = 7) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function displayShortUrl(shortCode, customPrefix = 'encurt.ar/') {
    const shortUrl = `${customPrefix}${shortCode}`;
    const fullUrl = `https://${shortUrl}`; 
    
    if (shortUrlText) {
        shortUrlText.textContent = shortUrl;
        shortUrlText.href = fullUrl;
    }
    if (resultDiv) resultDiv.classList.remove('hidden');
	
    // ✅ Gerar QR Code aqui, usando o fullUrl local
    const qrCodeContainer = document.getElementById("qrcode");
    if (qrCodeContainer) {
        qrCodeContainer.innerHTML = "";
        new QRCode(qrCodeContainer, {
            text: fullUrl,
            width: 128,
            height: 128,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H
        });
    }
}

if (shortenButton) {
    shortenButton.addEventListener('click', async () => {
        if (!auth || !db) {
            displayMessage("Firebase não inicializado. Encurtamento indisponível.", true);
            return;
        }

        if (!currentUserId) {
            if (auth.currentUser) {
                currentUserId = auth.currentUser.uid;
                isUserAnonymous = auth.currentUser.isAnonymous;
                updateUIForAuthState(auth.currentUser); // Garante que a UI esteja atualizada
            } else {
                try {
                    console.log("Tentando autenticação anônima para encurtar (currentUserId nulo)...");
                    const userCredential = await signInAnonymously(auth);
                    // onAuthStateChanged deve ter sido chamado, mas para garantir:
                    currentUserId = userCredential.user.uid;
                    isUserAnonymous = true;
                    updateUIForAuthState(userCredential.user);
                    console.log("Autenticado anonimamente para encurtar (currentUserId era nulo):", currentUserId);
                } catch (error) {
                    console.error("Erro na autenticação anônima durante o encurtamento (currentUserId nulo):", error);
                    displayMessage("Erro de autenticação ao tentar encurtar. Recarregue.", true);
                    return;
                }
            }
        }
        if (!currentUserId) { // Verificação final
            displayMessage("Não foi possível obter ID do usuário. Tente novamente.", true);
            return;
        }

        let longUrl = longUrlInput.value.trim();
        if (!longUrl) {
            displayMessage("Por favor, insira uma URL válida.", true);
            return;
        }

        // Validação rigorosa de URL
        if (!validateUrl(longUrl)) {
            displayMessage("Por favor, insira uma URL válida. Exemplo: https://www.google.com", true);
            return;
        }

        // Adiciona protocolo se não tiver
        if (!longUrl.startsWith('http://') && !longUrl.startsWith('https://')) {
            longUrl = 'https://' + longUrl;
        }

        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (resultDiv) resultDiv.classList.add('hidden');
        shortenButton.disabled = true;

        let shortCodeToUse = "";
        let isCustomCode = false;
        const customCodeValue = customShortCodeInput ? customShortCodeInput.value.trim() : "";

        if (!isUserAnonymous && customCodeValue !== "") {
            shortCodeToUse = customCodeValue.replace(/[^a-z0-9-]/gi, '').toLowerCase(); // Permite apenas letras, números, hífens
            if (shortCodeToUse !== customCodeValue) {
                 displayMessage("Código personalizado contém caracteres inválidos. Use apenas letras, números e hífens.", true);
                 if (loadingIndicator) loadingIndicator.classList.add('hidden');
                 shortenButton.disabled = false;
                 return;
            }
            if (shortCodeToUse.length === 0 || shortCodeToUse.length > 20) {
                 displayMessage("Código personalizado deve ter entre 1 e 20 caracteres.", true);
                 if (loadingIndicator) loadingIndicator.classList.add('hidden');
                 shortenButton.disabled = false;
                 return;
            }
            isCustomCode = true;
        } else {
            shortCodeToUse = generateShortCode();
        }

        try {
            const linkDocRef = doc(db, "artifacts", firestoreAppId, "public", "data", "links", shortCodeToUse);
            const docSnap = await getDoc(linkDocRef);

            if (docSnap.exists()) {
                console.warn("Código curto já existe:", shortCodeToUse);
                displayMessage(isCustomCode ? "Este código personalizado já está em uso. Tente outro." : "Ocorreu um erro (colisão de código). Tente novamente.", true);
                throw new Error("Código curto já em uso");
            }

            await setDoc(linkDocRef, {
                longUrl: longUrl,
                shortCode: shortCodeToUse,
                createdAt: serverTimestamp(),
                creatorUserId: currentUserId,
                isAnonymousLink: isUserAnonymous,
                isCustom: isCustomCode,
                clickCount: 0,
                lastAccessed: null
            });

            displayShortUrl(shortCodeToUse);
            if (longUrlInput) longUrlInput.value = '';
            if (customShortCodeInput) customShortCodeInput.value = '';
            displayMessage("URL encurtada com sucesso!", false);

        } catch (error) {
            console.error("Erro ao salvar no Firestore: ", error);
            if (!error.message.includes("Código curto já em uso")) {
                displayMessage("Erro ao encurtar a URL. Tente novamente.", true);
            }
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            shortenButton.disabled = false;
        }
    });
}

if (copyButton) {
    copyButton.addEventListener('click', () => {
        if (!shortUrlText) return;
        const urlToCopy = `https://${shortUrlText.textContent}`;
        
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(urlToCopy).then(() => {
                displayMessage("URL copiada para a área de transferência!", false);
            }).catch(err => {
                console.error('Erro ao copiar com navigator.clipboard: ', err);
                legacyCopy(urlToCopy);
            });
        } else {
            legacyCopy(urlToCopy);
        }
    });
}

function legacyCopy(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed"; 
    textArea.style.top = "-9999px";
    textArea.style.left = "-9999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        document.execCommand('copy');
        displayMessage("URL copiada para a área de transferência! (fallback)", false);
    } catch (errFallback) {
        console.error('Erro ao copiar texto (fallback): ', errFallback);
        displayMessage("Erro ao copiar. Por favor, copie manualmente.", true);
    }
    document.body.removeChild(textArea);
}

// Ad Placeholder Click Simulation (se você mantiver essa funcionalidade)
const adPlaceholders = document.querySelectorAll('.ad-placeholder');
if (adPlaceholders.length > 0) {
    adPlaceholders.forEach(ad => {
        ad.addEventListener('click', () => {
            const adTextElement = ad.querySelector('p');
            const adText = adTextElement ? adTextElement.textContent : 'Anúncio';
            displayMessage(`Anúncio "${adText}" clicado! (Simulação)`, false);
        });
    });
}


// Atualiza o ano no rodapé
const currentYearSpan = document.getElementById('currentYear');
if (currentYearSpan) {
    currentYearSpan.textContent = new Date().getFullYear();
}

// Chamada inicial para configurar a UI
// Se auth não inicializou (devido a erro de config, por ex.),
// updateUIForAuthState(null) será chamado dentro do bloco onAuthStateChanged ou no catch da inicialização.
if (!auth) { // Garante que a UI seja definida para um estado padrão se auth falhar completamente
    updateUIForAuthState(null);
}


// 🌗 Dark Mode Toggle
const darkModeToggle = document.getElementById('darkModeToggle');
if (darkModeToggle) {
  const sunIcon = darkModeToggle.querySelector('.sun');
  const moonIcon = darkModeToggle.querySelector('.moon');

  if (localStorage.getItem("theme") === "dark" || 
      (!("theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
    document.documentElement.classList.add("dark");
    sunIcon.classList.add("hidden");
    moonIcon.classList.remove("hidden");
  }

  darkModeToggle.addEventListener("click", () => {
    const isDark = document.documentElement.classList.toggle("dark");
    localStorage.setItem("theme", isDark ? "dark" : "light");
    sunIcon.classList.toggle("hidden", isDark);
    moonIcon.classList.toggle("hidden", !isDark);
  });
}

// 📥 Botões de Download QR e Preview
const downloadQRButton = document.getElementById("downloadQRButton");
const previewButton = document.getElementById("previewButton");

if (downloadQRButton) {
  downloadQRButton.addEventListener("click", () => {
    const canvas = document.querySelector("#qrcode canvas");
    if (!canvas) return alert("QR Code não disponível.");
    const link = document.createElement("a");
    link.href = canvas.toDataURL("image/png");
    link.download = "qrcode.png";
    link.click();
  });
}

if (previewButton) {
  previewButton.addEventListener("click", async () => {
    const shortUrlElement = document.getElementById("shortUrlText");
    if (!shortUrlElement || !shortUrlElement.textContent) {
        alert("URL encurtada não encontrada.");
        return;
    }

    // Extrair o código curto da URL exibida (ex: "encurt.ar/abc123" -> "abc123")
    const shortCode = shortUrlElement.textContent.split('/').pop();

    if (!shortCode) {
        alert("Código curto não encontrado.");
        return;
    }

    await showPreviewModal(shortCode);
  });
}

// Função para buscar a URL original no Firestore e exibir o modal de preview
async function showPreviewModal(shortCode) {
    const previewModal = document.getElementById('previewModal');
    const previewUrl = document.getElementById('previewUrl');
    const previewLoading = document.getElementById('previewLoading');
    const previewFrame = document.getElementById('previewFrame');
    const previewError = document.getElementById('previewError');
    const sitePreview = document.getElementById('sitePreview');

    if (!previewModal) return;

    try {
        // Mostrar o modal
        previewModal.classList.remove('hidden');

        // Mostrar loading
        previewLoading.classList.remove('hidden');
        previewFrame.classList.add('hidden');
        previewError.classList.add('hidden');

        // Buscar a URL original no Firestore
        const linkDocRef = doc(db, "artifacts", firestoreAppId, "public", "data", "links", shortCode);
        const docSnap = await getDoc(linkDocRef);

        if (!docSnap.exists()) {
            throw new Error("Link não encontrado");
        }

        const data = docSnap.data();
        const originalUrl = data.longUrl;

        if (!originalUrl) {
            throw new Error("URL original não encontrada");
        }

        // Exibir a URL original
        previewUrl.textContent = originalUrl;

        // Configurar o iframe
        sitePreview.src = originalUrl;

        // Esconder loading e mostrar frame
        previewLoading.classList.add('hidden');
        previewFrame.classList.remove('hidden');

        // Configurar timeout para detectar se o iframe não carregou
        const timeoutId = setTimeout(() => {
            previewFrame.classList.add('hidden');
            previewError.classList.remove('hidden');
        }, 10000); // 10 segundos

        // Detectar quando o iframe carregou com sucesso
        sitePreview.onload = () => {
            clearTimeout(timeoutId);
        };

        // Detectar erro no iframe
        sitePreview.onerror = () => {
            clearTimeout(timeoutId);
            previewFrame.classList.add('hidden');
            previewError.classList.remove('hidden');
        };

    } catch (error) {
        console.error("Erro ao carregar preview:", error);
        previewLoading.classList.add('hidden');
        previewFrame.classList.add('hidden');
        previewError.classList.remove('hidden');

        if (previewUrl) {
            previewUrl.textContent = "Erro ao carregar URL";
        }
    }
}

// Event listeners para os botões do modal de preview
const closePreviewModal = document.getElementById('closePreviewModal');
const proceedToUrl = document.getElementById('proceedToUrl');
const cancelPreview = document.getElementById('cancelPreview');
const previewModal = document.getElementById('previewModal');

if (closePreviewModal) {
    closePreviewModal.addEventListener('click', () => {
        hidePreviewModal();
    });
}

if (cancelPreview) {
    cancelPreview.addEventListener('click', () => {
        hidePreviewModal();
    });
}

if (proceedToUrl) {
    proceedToUrl.addEventListener('click', () => {
        const previewUrl = document.getElementById('previewUrl');
        if (previewUrl && previewUrl.textContent && previewUrl.textContent !== "Erro ao carregar URL") {
            window.open(previewUrl.textContent, '_blank');
            hidePreviewModal();
        }
    });
}

// Fechar modal clicando fora dele
if (previewModal) {
    previewModal.addEventListener('click', (e) => {
        if (e.target === previewModal) {
            hidePreviewModal();
        }
    });
}

// Função para esconder o modal de preview
function hidePreviewModal() {
    const previewModal = document.getElementById('previewModal');
    const sitePreview = document.getElementById('sitePreview');

    if (previewModal) {
        previewModal.classList.add('hidden');
    }

    // Limpar o iframe para parar o carregamento
    if (sitePreview) {
        sitePreview.src = 'about:blank';
    }
}