{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "firestore": {"database": "(default)", "location": "nam5", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**/*.@(html|htm)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.@(css|js)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}], "rewrites": [{"source": "/r/**", "function": "redirectFunction"}, {"source": "/[a-zA-Z0-9]*", "function": "redirectFunction"}, {"source": "**", "destination": "/index.html"}]}}