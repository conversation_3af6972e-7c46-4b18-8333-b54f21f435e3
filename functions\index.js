/**
 * Importa os módulos necessários do Firebase.
 * O 'firebase-functions' é para criar as Cloud Functions.
 * O 'firebase-admin' é para interagir com serviços do Firebase no backend
 * (como o Firestore).
 */
const {onRequest} = require("firebase-functions/v2/https");
const {onDocumentCreated} = require("firebase-functions/v2/firestore");
const admin = require("firebase-admin");

// Função para gerar HTML 404 com o mesmo layout da página 404.html
function generate404Html(title = "Link Não Encontrado", message = "Este link curto não existe ou foi removido.") {
  return `<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Página Não Encontrada - Encurt.ar</title>
  <meta name="description" content="A página que você procura não foi encontrada. Volte ao Encurt.ar para encurtar suas URLs.">
  <meta name="robots" content="noindex, nofollow">
  <meta name="googlebot" content="noindex, nofollow">
  <link rel="canonical" href="https://encurt.ar/">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-2M5C9TMJW5"></script>
  <script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-2M5C9TMJW5');
  </script>

  <!-- Recursos -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body { font-family: 'Inter', sans-serif; }
  </style>

</head>

<body class="bg-gradient-to-br from-sky-500 to-indigo-600 min-h-screen flex flex-col items-center justify-center p-4 selection:bg-sky-300 selection:text-sky-900">

  <div class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg text-center">
    <!-- Header com logo -->
    <header class="mb-6 sm:mb-8">
      <h1 class="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700">
        encurt.<span class="text-sky-400">ar</span>
      </h1>
      <p class="text-gray-600 mt-2 text-sm sm:text-base">Seu novo encurtador de links favorito!</p>
    </header>

    <!-- Ícone 404 -->
    <div class="mb-6">
      <div class="mx-auto w-24 h-24 bg-gradient-to-r from-sky-100 to-indigo-100 rounded-full flex items-center justify-center mb-4">
        <svg class="w-12 h-12 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
        </svg>
      </div>
      <h2 class="text-6xl font-bold text-sky-500 mb-2">404</h2>
      <h3 class="text-2xl font-semibold text-gray-800 mb-4">${title}</h3>
    </div>

    <!-- Mensagem -->
    <div class="mb-8">
      <p class="text-gray-600 mb-4">
        <strong>Erro 404:</strong> ${message}
      </p>

      <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h4 class="font-semibold text-red-800 mb-2">⚠️ Link não encontrado</h4>
        <p class="text-sm text-red-700 mb-3">
          Este link pode ter sido:
        </p>
        <ul class="text-sm text-red-600 space-y-1 text-left">
          <li>• Digitado incorretamente</li>
          <li>• Expirado ou removido</li>
          <li>• Nunca ter existido</li>
          <li>• Bloqueado por segurança</li>
        </ul>
      </div>

      <div class="bg-sky-50 rounded-lg p-4 mb-6">
        <h4 class="font-semibold text-gray-800 mb-2">✅ O que você pode fazer:</h4>
        <ul class="text-sm text-gray-600 space-y-1 text-left">
          <li>• Verificar se a URL está correta</li>
          <li>• Voltar à página inicial do Encurt.ar</li>
          <li>• Criar uma nova URL encurtada</li>
          <li>• Entrar em contato se precisar de ajuda</li>
        </ul>
      </div>
    </div>

    <!-- Botões de ação -->
    <div class="space-y-3">
      <a href="https://encurt.ar"
         class="w-full inline-block bg-gradient-to-r from-sky-500 to-indigo-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:from-sky-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
        🏠 Voltar ao Encurt.ar
      </a>

      <a href="https://encurt.ar"
         class="w-full inline-block bg-white text-sky-600 font-semibold py-3 px-6 rounded-lg border-2 border-sky-200 hover:bg-sky-50 transition-all duration-300">
        ✨ Criar Nova URL
      </a>

      <a href="https://encurt.ar/contact.html"
         class="w-full inline-block bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 hover:bg-gray-200 transition-all duration-300 text-sm">
        📧 Precisa de Ajuda?
      </a>
    </div>

    <!-- Footer -->
    <div class="mt-8 pt-6 border-t border-gray-200">
      <p class="text-xs text-gray-500">
        © 2025 Encurt.ar - Todos os direitos reservados
      </p>
    </div>
  </div>

  <!-- Script para analytics -->
  <script>
    // Registra o evento 404 no Google Analytics
    if (typeof gtag !== 'undefined') {
      // Evento específico para 404
      gtag('event', 'page_not_found', {
        'page_title': '404 - Página Não Encontrada',
        'page_location': window.location.href,
        'page_referrer': document.referrer,
        'custom_parameter': 'error_404_function'
      });

      // Page view para 404
      gtag('event', 'page_view', {
        page_title: '404 - Página Não Encontrada',
        page_location: window.location.href
      });
    }

    // Log para console (debug)
    console.log('404 Error - Page not found (Cloud Function):', window.location.href);
  </script>

</body>
</html>`;
}

// Inicializa o SDK do Firebase Admin.
try {
  admin.initializeApp();
} catch (e) {
  // Este erro pode acontecer se a função for inicializada múltiplas vezes
  // (ex: em testes locais). Numa implantação real no Firebase, isto
  // geralmente não é um problema.
  console.log(
      "Firebase admin initialization error " +
      "(pode ser ignorado em alguns cenários de teste):",
      e.message,
  );
}

// Obtém uma referência ao Firestore usando o banco padrão.
const db = admin.firestore();

/**
 * Define a Cloud Function chamada 'redirectFunction'.
 * Esta função é acionada por uma requisição HTTP.
 */
exports.redirectFunction = onRequest(async (req, res) => {
  // 1. Extrai o 'shortCode' do caminho da URL.
  // req.path será algo como "/AbCd123". Removemos a barra inicial.
  const shortCode = req.path.substring(1);

  // Lista de páginas estáticas que não devem ser processadas como shortCodes
  const staticPages = [
    "", // página principal
    "index.html",
    "about.html",
    "contact.html",
    "privacy.html",
    "terms.html",
    "404.html",
    "admin.html",
    "sitemap.xml",
    "robots.txt",
    "favicon.ico",
    "style.css",
    // Também incluir versões sem .html
    "about",
    "contact",
    "privacy",
    "terms",
    "admin"
  ];

  // Verifica se é uma página estática
  if (staticPages.includes(shortCode) || staticPages.includes(shortCode + ".html")) {
    console.log("Página estática detectada, deixando Firebase Hosting servir:", shortCode);
    // Não processa na função, deixa o Firebase Hosting servir o arquivo
    return;
  }

  // Validações mais robustas para shortCode
  if (!shortCode || shortCode.trim() === "") {
    console.log("ShortCode inválido ou ausente:", shortCode);
    // Deixa o Firebase Hosting servir a página principal
    return;
  }

  // Verifica se o shortCode tem formato válido
  // Deve ter entre 4-20 caracteres, apenas letras, números e alguns símbolos
  const shortCodeRegex = /^[a-zA-Z0-9_-]{4,20}$/;
  if (!shortCodeRegex.test(shortCode)) {
    console.log("ShortCode com formato inválido:", shortCode);
    res.status(404).send(generate404Html("Formato Inválido", "Este link não possui um formato válido."));
    return;
  }

  // Verifica se não é um shortCode reservado ou perigoso
  const reservedCodes = [
    "admin", "api", "www", "mail", "ftp", "localhost", "test", "dev",
    "staging", "prod", "production", "beta", "alpha", "demo", "docs",
    "help", "support", "blog", "news", "app", "mobile", "static",
    "assets", "cdn", "media", "images", "js", "css", "fonts"
  ];

  if (reservedCodes.includes(shortCode.toLowerCase())) {
    console.log("ShortCode reservado detectado:", shortCode);
    res.status(404).send(generate404Html("Link Reservado", "Este código é reservado pelo sistema."));
    return;
  }

  try {
    // 2. Constrói a referência ao documento no Firestore.
    // Estrutura correta: public/data/links/{shortCode}
    const linkDocRef = db
        .collection("public")
        .doc("data")
        .collection("links")
        .doc(shortCode);

    // 3. Tenta obter o documento.
    const docSnap = await linkDocRef.get();

    if (docSnap.exists) {
      // 4. Se o documento existir, obtém a URL longa.
      const data = docSnap.data();
      const longUrl = data.longUrl;

      if (longUrl) {
        // 5. Incrementa o contador de cliques e atualiza lastAccessed
        try {
          const currentClickCount = data.clickCount || 0;
          await linkDocRef.update({
            clickCount: currentClickCount + 1,
            lastAccessed: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log(
              `Contador atualizado para ${shortCode}: ${currentClickCount + 1}`,
          );
        } catch (updateError) {
          console.error("Erro ao atualizar contador de cliques:", updateError);
          // Continua com o redirecionamento mesmo se a atualização falhar
        }

        // 6. Redireciona o utilizador para a URL longa.
        // Usamos 301 para redirecionamento permanente.
        console.log(`Redirecionando ${shortCode} para ${longUrl}`);
        res.redirect(301, longUrl);
      } else {
        // Caso o documento exista mas não tenha a propriedade longUrl.
        console.warn("Documento encontrado mas sem longUrl:", shortCode);
        // Retorna 404 real usando o mesmo layout da página 404.html
        res.status(404).send(generate404Html("Link Corrompido", "Este link curto existe mas está corrompido."));
      }
    } else {
      // 6. Se o documento não for encontrado, o link não existe.
      // Retorna 404 real usando o mesmo layout da página 404.html
      console.log("ShortCode não encontrado no Firestore:", shortCode);

      res.status(404).send(generate404Html("Link Não Encontrado", "Este link curto não existe ou foi removido."));
    }
  } catch (error) {
    // Em caso de erro ao aceder ao Firestore ou outro erro inesperado.
    console.error(
        "Erro na Cloud Function ao processar o shortCode:",
        shortCode,
        error,
    );
    res.status(500).send("Ocorreu um erro ao processar o seu link.");
  }
});

/**
 * Cloud Function para processar mensagens de contato
 * Acionada quando um novo documento é criado na coleção 'contact-messages'
 */
exports.processContactMessage = onDocumentCreated(
    "contact-messages/{messageId}",
    async (event) => {
      const snap = event.data;
      const messageId = event.params.messageId;
      const messageData = snap.data();

      console.log("Nova mensagem de contato recebida:", messageId);
      console.log("Dados:", {
        name: messageData.name,
        email: messageData.email,
        subject: messageData.subject,
        timestamp: messageData.timestamp,
      });

      // Aqui você pode adicionar lógica para:
      // 1. Enviar email de notificação para o admin
      // 2. Enviar email de confirmação para o usuário
      // 3. Integrar com sistemas de CRM
      // 4. Enviar notificações para Slack/Discord

      try {
        // Marcar como processada
        await snap.ref.update({
          processed: true,
          processedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        console.log("Mensagem processada com sucesso:", messageId);
      } catch (error) {
        console.error("Erro ao processar mensagem:", error);
      }
    });

