// Script para migrar dados da estrutura antiga para a nova
const admin = require('firebase-admin');

// Inicializar Firebase Admin usando Application Default Credentials
admin.initializeApp({
  projectId: 'encurtar-app'
});

const db = admin.firestore();

async function createTestLink() {
  try {
    console.log('🔄 Criando link de teste...');

    const testData = {
      longUrl: 'https://www.google.com',
      shortCode: 'test123',
      clickCount: 0,
      createdAt: new Date(),
      creatorUserId: 'test-user',
      isAnonymousLink: false,
      isCustom: false,
      lastAccessed: null
    };

    // Salvar na nova estrutura: links/{shortCode}
    await db.collection('links').doc('test123').set(testData);

    console.log('✅ Link de teste criado com sucesso!');
    console.log('🔗 Teste: https://encurt.ar/test123');

    // Verificar se foi criado
    const doc = await db.collection('links').doc('test123').get();
    if (doc.exists) {
      console.log('✅ Documento confirmado no Firestore:', doc.data());
    } else {
      console.log('❌ Documento não encontrado após criação');
    }

  } catch (error) {
    console.error('❌ Erro ao criar link de teste:', error);
  }
}

// Executar criação do link de teste
createTestLink().then(() => {
  console.log('🏁 Script finalizado');
  process.exit(0);
});
