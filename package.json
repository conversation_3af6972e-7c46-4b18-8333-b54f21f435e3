{"devDependencies": {"firebase-tools": "^14.5.1"}, "name": "encurtar_projeto", "version": "1.0.0", "main": "index.js", "dependencies": {"abbrev": "^3.0.1", "abort-controller": "^3.0.0", "accepts": "^1.3.8", "agent-base": "^7.1.3", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "ansi-align": "^3.0.1", "ansi-escapes": "^4.3.2", "ansi-regex": "^6.1.0", "ansi-styles": "^4.3.0", "any-promise": "^1.3.0", "anymatch": "^3.1.3", "archiver": "^7.0.1", "archiver-utils": "^5.0.2", "argparse": "^1.0.10", "array-flatten": "^1.1.1", "arrify": "^2.0.1", "as-array": "^2.0.0", "ast-types": "^0.13.4", "async": "^3.2.6", "async-lock": "^1.4.1", "asynckit": "^0.4.0", "b4a": "^1.6.7", "balanced-match": "^1.0.2", "bare-events": "^2.5.4", "base64-js": "^1.5.1", "basic-auth": "^2.0.1", "basic-auth-connect": "^1.1.0", "basic-ftp": "^5.0.5", "bignumber.js": "^9.3.0", "binary-extensions": "^2.3.0", "bl": "^4.1.0", "body-parser": "^1.20.3", "boxen": "^5.1.2", "brace-expansion": "^1.1.11", "braces": "^3.0.3", "buffer": "^5.7.1", "buffer-crc32": "^1.0.0", "buffer-equal-constant-time": "^1.0.1", "bytes": "^3.1.2", "cacache": "^19.0.1", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "call-me-maybe": "^1.0.2", "camelcase": "^6.3.0", "chalk": "^5.4.1", "char-regex": "^1.0.2", "chardet": "^0.7.0", "chokidar": "^3.6.0", "chownr": "^2.0.0", "ci-info": "^2.0.0", "cjson": "^0.3.3", "cli-boxes": "^2.2.1", "cli-cursor": "^3.1.0", "cli-highlight": "^2.1.11", "cli-spinners": "^2.9.2", "cli-table3": "^0.6.5", "cli-width": "^4.1.0", "cliui": "^8.0.1", "clone": "^1.0.4", "color": "^3.2.1", "color-convert": "^1.9.3", "color-name": "^1.1.3", "color-string": "^1.9.1", "colorette": "^2.0.20", "colorspace": "^1.1.4", "combined-stream": "^1.0.8", "commander": "^5.1.0", "compress-commons": "^6.0.2", "compressible": "^2.0.18", "compression": "^1.8.0", "concat-map": "^0.0.1", "config-chain": "^1.1.13", "configstore": "^5.0.1", "connect": "^3.7.0", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.0.6", "core-util-is": "^1.0.3", "cors": "^2.8.5", "crc-32": "^1.2.2", "crc32-stream": "^6.0.0", "cross-env": "^7.0.3", "cross-spawn": "^7.0.6", "crypto-random-string": "^2.0.0", "csv-parse": "^5.6.0", "data-uri-to-buffer": "^6.0.2", "debug": "^2.6.9", "deep-equal-in-any-order": "^2.0.6", "deep-extend": "^0.6.0", "deep-freeze": "^0.0.1", "deep-is": "^0.1.4", "defaults": "^1.0.4", "degenerator": "^5.0.1", "delayed-stream": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.2.0", "discontinuous-range": "^1.0.0", "dot-prop": "^5.3.0", "dunder-proto": "^1.0.1", "duplexify": "^4.1.3", "eastasianwidth": "^0.2.0", "ecdsa-sig-formatter": "^1.0.11", "ee-first": "^1.1.1", "emoji-regex": "^8.0.0", "emojilib": "^2.4.0", "enabled": "^2.0.0", "encodeurl": "^2.0.0", "encoding": "^0.1.13", "end-of-stream": "^1.4.4", "env-paths": "^2.2.1", "environment": "^1.1.0", "err-code": "^2.0.3", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "escalade": "^3.2.0", "escape-goat": "^2.1.1", "escape-html": "^1.0.3", "escodegen": "^2.1.0", "esprima": "^4.0.1", "estraverse": "^5.3.0", "esutils": "^2.0.3", "etag": "^1.8.1", "event-target-shim": "^5.0.1", "events": "^3.3.0", "events-listener": "^1.1.0", "eventsource": "^3.0.7", "eventsource-parser": "^3.0.2", "exegesis": "^4.3.0", "exegesis-express": "^4.0.0", "exponential-backoff": "^3.1.2", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "extend": "^3.0.2", "external-editor": "^3.1.0", "fast-deep-equal": "^3.1.3", "fast-fifo": "^1.3.2", "fast-json-stable-stringify": "^2.1.0", "fast-uri": "^3.0.6", "fecha": "^4.2.3", "filesize": "^6.4.0", "fill-range": "^7.1.1", "finalhandler": "^1.3.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "fn.name": "^1.1.0", "foreground-child": "^3.3.1", "form-data": "^4.0.2", "forwarded": "^0.2.0", "fresh": "^0.5.2", "fs-extra": "^10.1.0", "fs-minipass": "^3.0.3", "function-bind": "^1.1.2", "fuzzy": "^0.1.3", "gaxios": "^6.7.1", "gcp-metadata": "^6.1.1", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-uri": "^6.0.4", "glob": "^10.4.5", "glob-parent": "^5.1.2", "glob-slash": "^1.0.0", "glob-slasher": "^1.0.1", "global-dirs": "^3.0.1", "google-auth-library": "^9.15.1", "google-gax": "^4.6.1", "google-logging-utils": "^0.0.2", "googleapis-common": "^7.2.0", "gopd": "^1.2.0", "graceful-fs": "^4.2.11", "gtoken": "^7.1.0", "has-flag": "^4.0.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "has-yarn": "^2.1.0", "hasown": "^2.0.2", "heap-js": "^2.6.0", "highlight.js": "^10.7.3", "http-cache-semantics": "^4.2.0", "http-errors": "^2.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.4.24", "ieee754": "^1.2.1", "ignore": "^7.0.5", "import-lazy": "^2.1.0", "imurmurhash": "^0.1.4", "inherits": "^2.0.4", "ini": "^2.0.0", "install-artifact-from-github": "^1.4.0", "ip-address": "^9.0.5", "ip-regex": "^4.3.0", "ipaddr.js": "^1.9.1", "is-arrayish": "^0.3.2", "is-binary-path": "^2.1.0", "is-buffer": "^1.1.6", "is-ci": "^2.0.0", "is-extglob": "^2.1.1", "is-fullwidth-code-point": "^3.0.0", "is-glob": "^4.0.3", "is-installed-globally": "^0.4.0", "is-interactive": "^1.0.0", "is-npm": "^5.0.0", "is-number": "^2.1.0", "is-obj": "^2.0.0", "is-path-inside": "^3.0.3", "is-promise": "^4.0.0", "is-stream": "^2.0.1", "is-stream-ended": "^0.1.4", "is-typedarray": "^1.0.0", "is-unicode-supported": "^0.1.0", "is-url": "^1.2.4", "is-wsl": "^1.1.0", "is-yarn-global": "^0.3.0", "is2": "^2.0.9", "isarray": "^1.0.0", "isexe": "^2.0.0", "isomorphic-fetch": "^3.0.0", "jackspeak": "^3.4.3", "jju": "^1.4.0", "join-path": "^1.1.1", "js-yaml": "^3.14.1", "jsbn": "^1.1.0", "json-bigint": "^1.0.0", "json-parse-helpfulerror": "^1.0.3", "json-ptr": "^3.1.1", "json-schema-traverse": "^1.0.0", "jsonfile": "^6.1.0", "jsonwebtoken": "^9.0.2", "jwa": "^2.0.1", "jws": "^4.0.0", "kind-of": "^3.2.2", "kuler": "^2.0.0", "lazystream": "^1.0.1", "leven": "^3.1.0", "libsodium": "^0.7.15", "libsodium-wrappers": "^0.7.15", "lodash": "^4.17.21", "lodash._objecttypes": "^2.4.1", "lodash.camelcase": "^4.3.0", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isobject": "^2.4.1", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.mapvalues": "^4.6.0", "lodash.once": "^4.1.1", "lodash.snakecase": "^4.1.1", "log-symbols": "^4.1.0", "logform": "^2.7.0", "long": "^5.3.2", "lru-cache": "^10.4.3", "lsofi": "^1.0.0", "make-dir": "^3.1.0", "make-fetch-happen": "^14.0.3", "marked": "^13.0.3", "marked-terminal": "^7.3.0", "math-intrinsics": "^1.1.0", "media-typer": "^0.3.0", "merge-descriptors": "^1.0.3", "methods": "^1.1.2", "mime": "^2.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-fn": "^2.1.0", "minimatch": "^3.1.2", "minimist": "^1.2.8", "minipass": "^7.1.2", "minipass-collect": "^2.0.1", "minipass-fetch": "^4.0.1", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "minipass-sized": "^1.0.3", "minizlib": "^3.0.2", "mkdirp": "^1.0.4", "moo": "^0.5.2", "morgan": "^1.10.0", "ms": "^2.0.0", "mute-stream": "^2.0.0", "mz": "^2.7.0", "nan": "^2.22.2", "nearley": "^2.20.1", "negotiator": "^0.6.3", "netmask": "^2.0.2", "node-emoji": "^2.2.0", "node-fetch": "^2.7.0", "node-gyp": "^11.2.0", "nopt": "^8.1.0", "normalize-path": "^3.0.0", "object-assign": "^4.1.1", "object-hash": "^3.0.0", "object-inspect": "^1.13.4", "on-finished": "^2.4.1", "on-headers": "^1.0.2", "once": "^1.4.0", "one-time": "^1.0.0", "onetime": "^5.1.2", "open": "^6.4.0", "openapi3-ts": "^3.2.0", "ora": "^5.4.1", "os-tmpdir": "^1.0.2", "p-defer": "^3.0.0", "p-limit": "^3.1.0", "p-map": "^7.0.3", "p-throttle": "^7.0.0", "pac-proxy-agent": "^7.2.0", "pac-resolver": "^7.0.1", "package-json-from-dist": "^1.0.1", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^6.0.1", "parseurl": "^1.3.3", "path-key": "^3.1.1", "path-scurry": "^1.11.1", "path-to-regexp": "^0.1.12", "pg": "^8.16.0", "pg-cloudflare": "^1.2.5", "pg-connection-string": "^2.9.0", "pg-int8": "^1.0.1", "pg-pool": "^3.10.0", "pg-protocol": "^1.10.0", "pg-types": "^2.2.0", "pgpass": "^1.0.5", "picomatch": "^2.3.1", "pkce-challenge": "^5.0.0", "portfinder": "^1.0.37", "postgres-array": "^2.0.0", "postgres-bytea": "^1.0.0", "postgres-date": "^1.0.7", "postgres-interval": "^1.2.0", "proc-log": "^5.0.0", "process": "^0.11.10", "process-nextick-args": "^2.0.1", "progress": "^2.0.3", "promise-breaker": "^6.0.0", "promise-retry": "^2.0.1", "proto-list": "^1.2.4", "proto3-json-serializer": "^2.0.2", "protobufjs": "^7.5.3", "proxy-addr": "^2.0.7", "proxy-agent": "^6.5.0", "proxy-from-env": "^1.1.0", "punycode": "^2.3.1", "pupa": "^2.1.1", "qs": "^6.13.0", "railroad-diagrams": "^1.0.0", "randexp": "^0.4.6", "range-parser": "^1.2.1", "raw-body": "^3.0.0", "rc": "^1.2.8", "re2": "^1.22.1", "readable-stream": "^4.7.0", "readdir-glob": "^1.1.3", "readdirp": "^3.6.0", "registry-auth-token": "^5.1.0", "registry-url": "^5.1.0", "require-directory": "^2.1.1", "require-from-string": "^2.0.2", "restore-cursor": "^3.1.0", "ret": "^0.1.15", "retry": "^0.13.1", "retry-request": "^7.0.2", "router": "^2.2.0", "safe-buffer": "^5.2.1", "safe-stable-stringify": "^2.5.0", "safer-buffer": "^2.1.2", "semver": "^7.7.2", "semver-diff": "^3.1.1", "send": "^0.19.0", "serve-static": "^1.16.2", "setprototypeof": "^1.2.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "signal-exit": "^4.1.0", "simple-swizzle": "^0.2.2", "skin-tone": "^2.0.0", "smart-buffer": "^4.2.0", "socks": "^2.8.4", "socks-proxy-agent": "^8.0.5", "sort-any": "^2.0.0", "source-map": "^0.6.1", "split2": "^4.2.0", "sprintf-js": "^1.0.3", "sql-formatter": "^15.6.2", "ssri": "^12.0.0", "stack-trace": "^0.0.10", "statuses": "^2.0.1", "stream-chain": "^2.2.5", "stream-events": "^1.0.5", "stream-json": "^1.9.1", "stream-shift": "^1.0.3", "streamx": "^2.22.1", "string_decoder": "^1.3.0", "string-width": "^4.2.3", "string-width-cjs": "^4.2.3", "strip-ansi": "^7.1.0", "strip-ansi-cjs": "^6.0.1", "strip-json-comments": "^2.0.1", "stubs": "^3.0.0", "superstatic": "^9.2.0", "supports-color": "^7.2.0", "supports-hyperlinks": "^3.2.0", "tar": "^6.2.1", "tar-stream": "^3.1.7", "tcp-port-used": "^1.0.2", "teeny-request": "^9.0.0", "text-decoder": "^1.2.3", "text-hex": "^1.0.0", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "through2": "^2.0.5", "tinyglobby": "^0.2.14", "tmp": "^0.2.3", "to-regex-range": "^5.0.1", "toidentifier": "^1.0.1", "toxic": "^1.0.1", "tr46": "^0.0.3", "triple-beam": "^1.4.1", "tslib": "^2.8.1", "tsscmp": "^1.0.6", "type-fest": "^0.21.3", "type-is": "^1.6.18", "typedarray-to-buffer": "^3.1.5", "undici-types": "^6.21.0", "unicode-emoji-modifier-base": "^1.0.0", "unique-filename": "^4.0.0", "unique-slug": "^5.0.0", "unique-string": "^2.0.0", "universal-analytics": "^0.5.3", "universalify": "^2.0.1", "unpipe": "^1.0.0", "update-notifier-cjs": "^5.1.7", "uri-js": "^4.4.1", "url-join": "^0.0.1", "url-template": "^2.0.8", "util-deprecate": "^1.0.2", "utils-merge": "^1.0.1", "uuid": "^8.3.2", "valid-url": "^1.0.9", "vary": "^1.1.2", "wcwidth": "^1.0.1", "webidl-conversions": "^3.0.1", "whatwg-fetch": "^3.6.20", "whatwg-url": "^5.0.0", "which": "^2.0.2", "widest-line": "^3.1.0", "winston": "^3.17.0", "winston-transport": "^4.9.0", "wrap-ansi": "^6.2.0", "wrap-ansi-cjs": "^7.0.0", "wrappy": "^1.0.2", "write-file-atomic": "^3.0.3", "ws": "^7.5.10", "xdg-basedir": "^4.0.0", "xtend": "^4.0.2", "y18n": "^5.0.8", "yallist": "^4.0.0", "yaml": "^2.8.0", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yocto-queue": "^0.1.0", "yoctocolors-cjs": "^2.1.2", "zip-stream": "^6.0.1", "zod": "^3.25.49", "zod-to-json-schema": "^3.24.5"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": ""}