# Configurações para melhor SEO e redirecionamentos
RewriteEngine On

# Redirecionar URLs com www para sem www
RewriteCond %{HTTP_HOST} ^www\.encurt\.ar [NC]
RewriteRule ^(.*)$ https://encurt.ar/$1 [R=301,L]

# Forçar HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://encurt.ar/$1 [R=301,L]

# Página de erro 404 personalizada
ErrorDocument 404 /404.html

# Configurações de cache - DESABILITADO TEMPORARIAMENTE
<IfModule mod_expires.c>
    ExpiresActive On
    # Cache desabilitado para forçar atualizações
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType text/css "access plus 0 seconds"
    ExpiresByType application/javascript "access plus 0 seconds"
    ExpiresByType image/png "access plus 0 seconds"
    ExpiresByType image/jpg "access plus 0 seconds"
    ExpiresByType image/jpeg "access plus 0 seconds"
    ExpiresByType image/gif "access plus 0 seconds"
    ExpiresByType image/svg+xml "access plus 0 seconds"
</IfModule>

# Headers para desabilitar cache
<IfModule mod_headers.c>
    # Desabilitar cache para HTML, CSS e JS
    <FilesMatch "\.(html|htm|css|js)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# Não fazer cache da página 404
<Files "404.html">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </IfModule>
</Files>

# Configurações de segurança
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
