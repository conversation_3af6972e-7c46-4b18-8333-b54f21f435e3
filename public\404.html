<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Página Não Encontrada - Encurt.ar</title>
  <meta name="description" content="A página que você procura não foi encontrada. Volte ao Encurt.ar para encurtar suas URLs.">
  <meta name="robots" content="noindex, nofollow">
  <meta name="googlebot" content="noindex, nofollow">
  <link rel="canonical" href="https://encurt.ar/">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-L12DSJ4R7D"></script>
  <script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-L12DSJ4R7D');
  </script>

  <!-- Recursos -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body { font-family: 'Inter', sans-serif; }
  </style>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-L12DSJ4R7D"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-L12DSJ4R7D');
</script>

</head>

<body class="bg-gradient-to-br from-sky-500 to-indigo-600 min-h-screen selection:bg-sky-300 selection:text-sky-900 flex flex-col">

  <!-- Menu Superior -->
  <nav class="bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo/Brand -->
        <div class="flex items-center">
          <a href="/" class="flex items-center space-x-2 text-white hover:text-sky-200 transition-colors">
            <svg class="h-8 w-8" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span class="text-xl font-bold">encurt.<span class="text-sky-200">ar</span></span>
          </a>
        </div>

        <!-- Menu Central -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="/" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
            <span>Início</span>
          </a>
          <a href="/" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span>Encurtar</span>
          </a>
          <a href="/about.html" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>Sobre</span>
          </a>
          <a href="/contact.html" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <span>Contato</span>
          </a>
        </div>

        <!-- Menu Mobile Button -->
        <button id="mobileMenuButton" class="md:hidden text-white hover:text-sky-200 p-2 rounded-md transition-colors">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>

      <!-- Mobile Menu -->
      <div id="mobileMenu" class="hidden md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <a href="/" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Início
          </a>
          <a href="/" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Encurtar
          </a>
          <a href="/about.html" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Sobre
          </a>
          <a href="/contact.html" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Contato
          </a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Conteúdo Principal -->
  <div class="flex-1 flex items-center justify-center p-4 min-h-0">
    <div class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg text-center">
    <!-- Header com logo -->
    <header class="mb-6 sm:mb-8">
      <h1 class="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700">
        encurt.<span class="text-sky-400">ar</span>
      </h1>
      <p class="text-gray-600 mt-2 text-sm sm:text-base">Seu novo encurtador de links favorito!</p>
    </header>

    <!-- Ícone 404 -->
    <div class="mb-6">
      <div class="mx-auto w-24 h-24 bg-gradient-to-r from-sky-100 to-indigo-100 rounded-full flex items-center justify-center mb-4">
        <svg class="w-12 h-12 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
        </svg>
      </div>
      <h2 class="text-6xl font-bold text-sky-500 mb-2">404</h2>
      <h3 class="text-2xl font-semibold text-gray-800 mb-4">Página Não Encontrada</h3>
    </div>

    <!-- Mensagem -->
    <div class="mb-8">
      <p class="text-gray-600 mb-4">
        <strong>Erro 404:</strong> A página ou link encurtado que você procura não existe, expirou ou foi digitado incorretamente.
      </p>

      <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h4 class="font-semibold text-red-800 mb-2">⚠️ Link não encontrado</h4>
        <p class="text-sm text-red-700 mb-3">
          Este link pode ter sido:
        </p>
        <ul class="text-sm text-red-600 space-y-1 text-left">
          <li>• Digitado incorretamente</li>
          <li>• Expirado ou removido</li>
          <li>• Nunca ter existido</li>
          <li>• Bloqueado por segurança</li>
        </ul>
      </div>

      <div class="bg-sky-50 rounded-lg p-4 mb-6">
        <h4 class="font-semibold text-gray-800 mb-2">✅ O que você pode fazer:</h4>
        <ul class="text-sm text-gray-600 space-y-1 text-left">
          <li>• Verificar se a URL está correta</li>
          <li>• Voltar à página inicial do Encurt.ar</li>
          <li>• Criar uma nova URL encurtada</li>
          <li>• Entrar em contato se precisar de ajuda</li>
        </ul>
      </div>
    </div>

    <!-- Botões de ação -->
    <div class="space-y-3">
      <a href="https://encurt.ar"
         class="w-full inline-block bg-gradient-to-r from-sky-500 to-indigo-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:from-sky-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
        🏠 Voltar ao Encurt.ar
      </a>

      <a href="https://encurt.ar"
         class="w-full inline-block bg-white text-sky-600 font-semibold py-3 px-6 rounded-lg border-2 border-sky-200 hover:bg-sky-50 transition-all duration-300">
        ✨ Criar Nova URL
      </a>

      <a href="https://encurt.ar/contact.html"
         class="w-full inline-block bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 hover:bg-gray-200 transition-all duration-300 text-sm">
        📧 Precisa de Ajuda?
      </a>
    </div>

    <!-- Footer -->
    <div class="mt-8 pt-6 border-t border-gray-200">
      <p class="text-xs text-gray-500">
        © 2025 Encurt.ar - Todos os direitos reservados
      </p>
    </div>
  </div>
  </div>

  <!-- Footer Global -->
  <footer class="bg-white/10 backdrop-blur-md border-t border-white/20 mt-auto">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Links de navegação centralizados -->
      <div class="text-center mb-3">
        <div class="flex flex-wrap justify-center gap-6 text-sm">
          <a href="/about.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Sobre</a>
          <a href="/contact.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Contato</a>
          <a href="/privacy.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Privacidade</a>
          <a href="/terms.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Termos de Uso</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Script para analytics -->
  <script>
    // Registra o evento 404 no Google Analytics
    if (typeof gtag !== 'undefined') {
      // Evento específico para 404
      gtag('event', 'page_not_found', {
        'page_title': '404 - Página Não Encontrada',
        'page_location': window.location.href,
        'page_referrer': document.referrer,
        'custom_parameter': 'error_404'
      });

      // Page view para 404
      gtag('event', 'page_view', {
        page_title: '404 - Página Não Encontrada',
        page_location: window.location.href
      });
    }

    // Opcional: Enviar para sistema de logs interno
    if (window.fetch) {
      fetch('/api/log-404', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: window.location.href,
          referrer: document.referrer,
          timestamp: new Date().toISOString()
        })
      }).catch(() => {}); // Silenciar erros
    }

    // Menu mobile toggle
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const mobileMenu = document.getElementById('mobileMenu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }
  </script>

</body>
</html>
