<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Mensagens de Contato | Encurt.ar</title>
    <meta name="robots" content="noindex, nofollow">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>

<body class="bg-gray-100 min-h-screen">
    <div class="max-w-7xl mx-auto py-6 px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Painel Administrativo</h1>
                    <p class="text-gray-600">Mensagens de Contato - Encurt.ar</p>
                </div>
                <div class="flex space-x-4">
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        🔄 Atualizar
                    </button>
                    <a href="/" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                        🏠 Voltar ao Site
                    </a>
                </div>
            </div>
        </div>

        <!-- Login Section -->
        <div id="loginSection" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Acesso Administrativo</h2>
            <div class="max-w-md">
                <input type="password" id="adminPassword" placeholder="Senha de administrador" 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg mb-4">
                <button id="loginBtn" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg">
                    🔐 Entrar
                </button>
            </div>
        </div>

        <!-- Stats Section -->
        <div id="statsSection" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-700">Total de Mensagens</h3>
                <p id="totalMessages" class="text-3xl font-bold text-blue-600">-</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-700">Não Lidas</h3>
                <p id="unreadMessages" class="text-3xl font-bold text-red-600">-</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-700">Hoje</h3>
                <p id="todayMessages" class="text-3xl font-bold text-green-600">-</p>
            </div>
        </div>

        <!-- Messages Section -->
        <div id="messagesSection" class="hidden bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold">Mensagens de Contato</h2>
            </div>
            <div id="messagesList" class="divide-y divide-gray-200">
                <!-- Messages will be loaded here -->
            </div>
            <div id="loadingMessages" class="p-8 text-center text-gray-500">
                Carregando mensagens...
            </div>
            <div id="noMessages" class="hidden p-8 text-center text-gray-500">
                Nenhuma mensagem encontrada.
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, query, orderBy, getDocs, doc, updateDoc } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Configuração do Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyAkJ1C3NXyzAfJveWiQJVYr-OAISg_Jfh8",
            authDomain: "projeto-encurtar.firebaseapp.com",
            projectId: "projeto-encurtar",
            storageBucket: "projeto-encurtar.firebasestorage.app",
            messagingSenderId: "638723124202",
            appId: "1:638723124202:web:65c4306e94aae917fb6093"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let isAuthenticated = false;

        // Elementos DOM
        const loginSection = document.getElementById('loginSection');
        const statsSection = document.getElementById('statsSection');
        const messagesSection = document.getElementById('messagesSection');
        const adminPassword = document.getElementById('adminPassword');
        const loginBtn = document.getElementById('loginBtn');
        const refreshBtn = document.getElementById('refreshBtn');

        // Função de login simples (senha hardcoded para demonstração)
        function checkPassword() {
            const password = adminPassword.value;
            // ALTERE ESTA SENHA PARA UMA SENHA SEGURA!
            if (password === 'SuaSenhaSegura2025!') {
                isAuthenticated = true;
                loginSection.classList.add('hidden');
                statsSection.classList.remove('hidden');
                messagesSection.classList.remove('hidden');
                loadMessages();
                return true;
            } else {
                alert('Senha incorreta!');
                return false;
            }
        }

        // Carregar mensagens
        async function loadMessages() {
            try {
                const messagesQuery = query(
                    collection(db, "contact-messages"),
                    orderBy("timestamp", "desc")
                );
                
                const querySnapshot = await getDocs(messagesQuery);
                const messagesList = document.getElementById('messagesList');
                const loadingMessages = document.getElementById('loadingMessages');
                const noMessages = document.getElementById('noMessages');
                
                loadingMessages.classList.add('hidden');
                
                if (querySnapshot.empty) {
                    noMessages.classList.remove('hidden');
                    return;
                }

                messagesList.innerHTML = '';
                let totalCount = 0;
                let unreadCount = 0;
                let todayCount = 0;
                const today = new Date().toDateString();

                querySnapshot.forEach((doc) => {
                    const data = doc.data();
                    totalCount++;
                    
                    if (!data.read) unreadCount++;
                    
                    const messageDate = data.timestamp?.toDate();
                    if (messageDate && messageDate.toDateString() === today) {
                        todayCount++;
                    }

                    const messageElement = createMessageElement(doc.id, data);
                    messagesList.appendChild(messageElement);
                });

                // Atualizar estatísticas
                document.getElementById('totalMessages').textContent = totalCount;
                document.getElementById('unreadMessages').textContent = unreadCount;
                document.getElementById('todayMessages').textContent = todayCount;

            } catch (error) {
                console.error('Erro ao carregar mensagens:', error);
                alert('Erro ao carregar mensagens. Verifique o console.');
            }
        }

        // Criar elemento de mensagem
        function createMessageElement(id, data) {
            const div = document.createElement('div');
            div.className = `p-6 ${!data.read ? 'bg-blue-50' : ''}`;
            
            const timestamp = data.timestamp?.toDate();
            const formattedDate = timestamp ? 
                timestamp.toLocaleString('pt-BR') : 'Data não disponível';

            div.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="flex-1">
                        <h3 class="font-semibold text-lg">${data.name || 'Nome não informado'}</h3>
                        <p class="text-gray-600">${data.email || 'Email não informado'}</p>
                        <span class="inline-block bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm mt-1">
                            ${data.subject || 'Sem assunto'}
                        </span>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">${formattedDate}</p>
                        ${!data.read ? '<span class="inline-block bg-red-500 text-white px-2 py-1 rounded text-xs">NOVA</span>' : ''}
                    </div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-800">${data.message || 'Mensagem vazia'}</p>
                </div>
                <div class="mt-4 flex space-x-2">
                    <button onclick="markAsRead('${id}')" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                        ✓ Marcar como Lida
                    </button>
                    <a href="mailto:${data.email}?subject=Re: ${data.subject}&body=Olá ${data.name},%0A%0A" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                        📧 Responder
                    </a>
                </div>
            `;
            
            return div;
        }

        // Marcar como lida
        window.markAsRead = async function(messageId) {
            try {
                await updateDoc(doc(db, "contact-messages", messageId), {
                    read: true,
                    readAt: new Date()
                });
                loadMessages(); // Recarregar
            } catch (error) {
                console.error('Erro ao marcar como lida:', error);
            }
        };

        // Event listeners
        loginBtn.addEventListener('click', checkPassword);
        adminPassword.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') checkPassword();
        });
        refreshBtn.addEventListener('click', () => {
            if (isAuthenticated) loadMessages();
        });

        // Auto-refresh a cada 30 segundos
        setInterval(() => {
            if (isAuthenticated) loadMessages();
        }, 30000);
    </script>
</body>
</html>
