<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contato - encurt.ar</title>
    
    <!-- Google AdSense -->
    <meta name="google-adsense-account" content="ca-pub-****************">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-L12DSJ4R7D"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-L12DSJ4R7D');
    </script>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">

    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-L12DSJ4R7D"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-L12DSJ4R7D');
</script>

</head>

<body class="bg-gradient-to-br from-sky-500 to-indigo-600 min-h-screen selection:bg-sky-300 selection:text-sky-900 flex flex-col">

  <!-- Menu Superior -->
  <nav class="bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo/Brand -->
        <div class="flex items-center">
          <a href="/" class="flex items-center space-x-2 text-white hover:text-sky-200 transition-colors">
            <svg class="h-8 w-8" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span class="text-xl font-bold">encurt.<span class="text-sky-200">ar</span></span>
          </a>
        </div>

        <!-- Menu Central -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="/" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
            <span>Início</span>
          </a>
          <a href="/" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span>Encurtar</span>
          </a>
          <a href="/about.html" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>Sobre</span>
          </a>
          <a href="/contact.html" class="text-sky-200 bg-white/10 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <span>Contato</span>
          </a>
        </div>

        <!-- Menu Mobile Button -->
        <button id="mobileMenuButton" class="md:hidden text-white hover:text-sky-200 p-2 rounded-md transition-colors">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>

      <!-- Mobile Menu -->
      <div id="mobileMenu" class="hidden md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <a href="/" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Início
          </a>
          <a href="/" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Encurtar
          </a>
          <a href="/about.html" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Sobre
          </a>
          <a href="/contact.html" class="text-sky-200 bg-white/10 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Contato
          </a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Conteúdo Principal -->
  <div class="flex-1 p-4">
    <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-2xl p-6 sm:p-8 my-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700 mb-2">
                encurt.<span class="text-sky-400">ar</span>
            </h1>
            <h2 class="text-2xl font-semibold text-gray-800">Entre em Contato</h2>
            <p class="text-gray-600 mt-2">Estamos aqui para ajudar você!</p>
        </header>

        <div class="grid md:grid-cols-2 gap-8">
            <!-- Formulário de Contato -->
            <div class="space-y-6">
                <h3 class="text-xl font-semibold text-gray-800">Envie uma Mensagem</h3>
                
                <form id="contactForm" class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome Completo *</label>
                        <input type="text" id="name" name="name" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors"
                            placeholder="Seu nome completo">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                        <input type="email" id="email" name="email" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors"
                            placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Assunto *</label>
                        <select id="subject" name="subject" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors">
                            <option value="">Selecione um assunto</option>
                            <option value="suporte">Suporte Técnico</option>
                            <option value="bug">Reportar Bug</option>
                            <option value="feature">Sugestão de Funcionalidade</option>
                            <option value="account">Problemas com Conta</option>
                            <option value="privacy">Questões de Privacidade</option>
                            <option value="business">Parcerias/Negócios</option>
                            <option value="other">Outros</option>
                        </select>
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Mensagem *</label>
                        <textarea id="message" name="message" rows="5" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors resize-vertical"
                            placeholder="Descreva sua dúvida, problema ou sugestão..."></textarea>
                    </div>

                    <div class="flex items-start">
                        <input type="checkbox" id="privacy" name="privacy" required
                            class="mt-1 h-4 w-4 text-sky-600 focus:ring-sky-500 border-gray-300 rounded">
                        <label for="privacy" class="ml-2 text-sm text-gray-700">
                            Li e concordo com a <a href="/privacy.html" class="text-sky-600 hover:underline" target="_blank">Política de Privacidade</a> *
                        </label>
                    </div>

                    <button type="submit"
                        class="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-all duration-300">
                        Enviar Mensagem
                    </button>
                </form>

                <div id="contactMessage" class="hidden p-4 rounded-lg"></div>
            </div>

            <!-- Informações de Contato -->
            <div class="space-y-6">
                <h3 class="text-xl font-semibold text-gray-800">Informações de Contato</h3>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-sky-500 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <div>
                            <h4 class="font-semibold text-gray-800">Email</h4>
                            <p class="text-gray-600"><EMAIL></p>
                            <p class="text-sm text-gray-500">Respondemos em até 24 horas</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-sky-500 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h4 class="font-semibold text-gray-800">Horário de Atendimento</h4>
                            <p class="text-gray-600">Segunda a Sexta: 9h às 18h</p>
                            <p class="text-gray-600">Sábado: 9h às 14h</p>
                            <p class="text-sm text-gray-500">Horário de Brasília (GMT-3)</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-sky-500 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <div>
                            <h4 class="font-semibold text-gray-800">Localização</h4>
                            <p class="text-gray-600">Brasil</p>
                            <p class="text-sm text-gray-500">Hospedado no Google Firebase</p>
                        </div>
                    </div>
                </div>

                <div class="bg-sky-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 mb-2">Perguntas Frequentes</h4>
                    <div class="space-y-2 text-sm">
                        <details class="cursor-pointer">
                            <summary class="font-medium text-gray-700 hover:text-sky-600">Como excluir minha conta?</summary>
                            <p class="mt-1 text-gray-600 pl-4">Entre em contato conosco solicitando a exclusão. Removeremos todos os seus dados em até 30 dias.</p>
                        </details>
                        <details class="cursor-pointer">
                            <summary class="font-medium text-gray-700 hover:text-sky-600">Posso recuperar URLs excluídas?</summary>
                            <p class="mt-1 text-gray-600 pl-4">URLs excluídas não podem ser recuperadas. Certifique-se antes de excluir.</p>
                        </details>
                        <details class="cursor-pointer">
                            <summary class="font-medium text-gray-700 hover:text-sky-600">O serviço é gratuito?</summary>
                            <p class="mt-1 text-gray-600 pl-4">Sim! O encurt.ar é totalmente gratuito e financiado por anúncios.</p>
                        </details>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">⚠️ Importante</h4>
                    <p class="text-sm text-yellow-700">
                        Para questões urgentes de segurança ou abuso, entre em contato imediatamente. 
                        Não toleramos spam, phishing ou conteúdo malicioso.
                    </p>
                </div>
            </div>
        </div>

        <div class="mt-8 text-center">
            <a href="/" class="inline-flex items-center px-4 py-2 bg-sky-500 hover:bg-sky-600 text-white font-semibold rounded-lg transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Voltar ao Início
            </a>
        </div>
    </div>
  </div>

  <!-- Footer Global -->
  <footer class="bg-white/10 backdrop-blur-md border-t border-white/20 mt-auto">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Links de navegação centralizados -->
      <div class="text-center mb-3">
        <div class="flex flex-wrap justify-center gap-6 text-sm">
          <a href="/about.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Sobre</a>
          <a href="/contact.html" class="text-sky-200 hover:text-white hover:underline transition-colors font-medium">Contato</a>
          <a href="/privacy.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Privacidade</a>
          <a href="/terms.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Termos de Uso</a>
        </div>
      </div>
    </div>
  </footer>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getFirestore, collection, addDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Configuração do Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyC7QSYVWJsKX0UxhkK8zthnzcwH3A9AgbY",
            authDomain: "encurtar-app.firebaseapp.com",
            projectId: "encurtar-app",
            storageBucket: "encurtar-app.firebasestorage.app",
            messagingSenderId: "730701938603",
            appId: "1:730701938603:web:004b3b96540402cb521cee",
            measurementId: "G-L12DSJ4R7D"
        };

        // Inicializar Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Função para mostrar mensagem
        function showMessage(text, isError = false) {
            const messageDiv = document.getElementById('contactMessage');
            messageDiv.className = `p-4 rounded-lg ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`;
            messageDiv.textContent = text;
            messageDiv.classList.remove('hidden');

            setTimeout(() => {
                messageDiv.classList.add('hidden');
            }, 5000);
        }

        // Manipular envio do formulário
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            // Mostrar loading
            submitButton.textContent = 'Enviando...';
            submitButton.disabled = true;

            try {
                // Coletar dados do formulário
                const formData = {
                    name: document.getElementById('name').value,
                    email: document.getElementById('email').value,
                    subject: document.getElementById('subject').value,
                    message: document.getElementById('message').value,
                    timestamp: serverTimestamp(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };

                // Salvar no Firestore
                await addDoc(collection(db, "contact-messages"), formData);

                // Mostrar sucesso
                showMessage('Mensagem enviada com sucesso! Entraremos em contato em breve.');

                // Reset form
                this.reset();

                // Registrar evento no Google Analytics
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'contact_form_submit', {
                        event_category: 'engagement',
                        event_label: formData.subject
                    });
                }

            } catch (error) {
                console.error('Erro ao enviar mensagem:', error);
                showMessage('Erro ao enviar mensagem. Tente novamente ou entre em contato por email.', true);
            } finally {
                // Restaurar botão
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        });

        // Menu mobile toggle
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuButton && mobileMenu) {
          mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
          });
        }
    </script>
</body>
</html>
