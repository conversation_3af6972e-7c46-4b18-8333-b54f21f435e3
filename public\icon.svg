<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="192" height="192" rx="24" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Link icon -->
  <g transform="translate(48, 48)">
    <path d="M36 24H60C66.6274 24 72 29.3726 72 36V60C72 66.6274 66.6274 72 60 72H36C29.3726 72 24 66.6274 24 60V36C24 29.3726 29.3726 24 36 24Z" stroke="white" stroke-width="4" fill="none"/>
    <path d="M48 48L72 24" stroke="white" stroke-width="4" stroke-linecap="round"/>
    <path d="M60 24H72V36" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Text "ar" -->
  <text x="96" y="140" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white" text-anchor="middle">ar</text>
</svg>
