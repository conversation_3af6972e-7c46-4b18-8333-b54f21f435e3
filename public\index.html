<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>encurt.ar - Encurtador de Links Gratuito e Rápido</title>

  <!-- Desabilitar cache durante desenvolvimento -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <!-- SEO Meta Tags -->
  <meta name="description" content="Encurte seus links gratuitamente com o encurt.ar! Transforme URLs longas em links curtos e compartilháveis. Rápido, seguro e fácil de usar. Crie sua conta e gerencie seus links.">
  <meta name="keywords" content="encurtador de links, URL curta, encurtar link, link shortener, encurt.ar, compartilhar links">
  <meta name="author" content="encurt.ar">
  <meta name="robots" content="index, follow">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://encurt.ar/">
  <meta property="og:title" content="encurt.ar - Encurtador de Links Gratuito">
  <meta property="og:description" content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro.">
  <meta property="og:image" content="https://encurt.ar/og-image.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://encurt.ar/">
  <meta property="twitter:title" content="encurt.ar - Encurtador de Links Gratuito">
  <meta property="twitter:description" content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro.">
  <meta property="twitter:image" content="https://encurt.ar/og-image.png">

  <!-- Google AdSense -->
  <meta name="google-adsense-account" content="ca-pub-****************">

  <!-- PWA Meta Tags -->
  <link rel="manifest" href="/site.webmanifest">
  <meta name="theme-color" content="#0ea5e9">
  <link rel="apple-touch-icon" href="/icon.svg">
  <link rel="icon" type="image/svg+xml" href="/icon.svg">
  <link rel="shortcut icon" href="/favicon.ico">

  <!-- Preload recursos críticos -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.tailwindcss.com">

  <!-- Outros recursos -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="style.css?v=1.0.1">

</head>

<body
  class="bg-gradient-to-br from-sky-500 to-indigo-600 min-h-screen selection:bg-sky-300 selection:text-sky-900 flex flex-col">

  <!-- Menu Superior -->
  <nav class="bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo/Brand -->
        <div class="flex items-center">
          <button id="logoButton" class="flex items-center space-x-2 text-white hover:text-sky-200 transition-colors">
            <svg class="h-8 w-8" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span class="text-xl font-bold">encurt.<span class="text-sky-200">ar</span></span>
          </button>
        </div>

        <!-- Menu Central -->
        <div class="hidden md:flex items-center space-x-8">
          <button id="menuInicio" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
            <span>Início</span>
          </button>
          <button id="menuEncurtar" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
            </svg>
            <span>Encurtar</span>
          </button>
          <button id="menuQRCode" class="text-white hover:text-sky-200 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
            </svg>
            <span>QR Code</span>
          </button>
        </div>

        <!-- Menu Direita - Usuário -->
        <div class="flex items-center space-x-4">
          <!-- Dark Mode Toggle -->
          <button id="darkModeToggle" class="text-white hover:text-sky-200 p-2 rounded-md transition-colors">
            <svg class="sun h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>
            <svg class="moon h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
            </svg>
          </button>

          <!-- User Menu -->
          <div class="relative">
            <button id="userMenuButton" class="text-white hover:text-sky-200 p-2 rounded-md transition-colors flex items-center space-x-1">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>

            <!-- Dropdown Menu -->
            <div id="userDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
              <div id="userDropdownContent">
                <!-- Conteúdo será preenchido pelo JavaScript -->
              </div>
            </div>
          </div>

          <!-- Mobile Menu Button -->
          <button id="mobileMenuButton" class="md:hidden text-white hover:text-sky-200 p-2 rounded-md transition-colors">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div id="mobileMenu" class="hidden md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <button id="mobileMenuInicio" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Início
          </button>
          <button id="mobileMenuEncurtar" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            Encurtar
          </button>
          <button id="mobileMenuQRCode" class="text-white hover:text-sky-200 block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors">
            QR Code
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Conteúdo Principal -->
  <div class="flex-1 flex items-center justify-center p-4 min-h-0">
    <div class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg">
    <header class="text-center mb-6 sm:mb-8">
      <h1
        class="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700">
        encurt.<span class="text-sky-400">ar</span>
      </h1>
      <p class="text-gray-600 mt-2 text-sm sm:text-base">Seu novo encurtador de links favorito!</p>
    </header>

    <div id="messageContainer" class="mb-4 space-y-2">
      <!-- Mensagens de erro/sucesso serão inseridas aqui pelo JavaScript -->
    </div>

    <!-- Seção de Autenticação (Login/Cadastro) -->
    <section id="authSection" class="mb-6">
      <!-- Formulários de Login/Cadastro (inicialmente ocultos ou um deles visível) -->
      <div id="authForms" class="hidden">
        <!-- Formulário de Login -->
        <form id="loginForm" class="space-y-4 mb-4">
          <h3 class="text-lg font-semibold text-gray-700">Login</h3>
          <div>
            <label for="loginEmail" class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" id="loginEmail" name="loginEmail" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="loginEmailValidation" class="mt-1 text-xs hidden"></div>
          </div>
          <div>
            <label for="loginPassword" class="block text-sm font-medium text-gray-700">Senha</label>
            <input type="password" id="loginPassword" name="loginPassword" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500">
          </div>
          <button type="submit"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Entrar</button>
          <p class="text-xs text-center text-gray-500">Não tem conta? <button type="button" id="showSignUp" class="text-sky-600 hover:underline font-medium">Cadastre-se</button></p>
          <p class="text-xs text-center text-gray-500 mt-2"><button type="button" id="forgotPassword" class="text-sky-600 hover:underline font-medium">Esqueci minha senha</button></p>
          <button type="button" id="backToMainFromLogin" class="w-full mt-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Voltar</button>
        </form>

        <!-- Formulário de Cadastro -->
        <form id="signUpForm" class="space-y-4 mb-4 hidden">
          <h3 class="text-lg font-semibold text-gray-700">Crie sua conta</h3>

          <!-- Nome Completo -->
          <div>
            <label for="signUpFullName" class="block text-sm font-medium text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Nome Completo
            </label>
            <input type="text" id="signUpFullName" name="signUpFullName" required
              placeholder="Digite seu nome completo"
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpFullNameValidation" class="mt-1 text-xs hidden"></div>
          </div>

          <!-- Nome de Usuário -->
          <div>
            <label for="signUpUsername" class="block text-sm font-medium text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Nome de usuário
            </label>
            <input type="text" id="signUpUsername" name="signUpUsername" required
              placeholder="ex: meuusuario123"
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpUsernameValidation" class="mt-1 text-xs hidden"></div>
            <p class="text-xs text-gray-500 mt-1">Apenas letras, números e underscore. Mín. 3 caracteres.</p>
          </div>

          <!-- Email -->
          <div>
            <label for="signUpEmail" class="block text-sm font-medium text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
              Endereço de e-mail
            </label>
            <input type="email" id="signUpEmail" name="signUpEmail" required
              placeholder="<EMAIL>"
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpEmailValidation" class="mt-1 text-xs hidden"></div>
          </div>

          <!-- Senha -->
          <div>
            <label for="signUpPassword" class="block text-sm font-medium text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Digite sua senha
            </label>
            <input type="password" id="signUpPassword" name="signUpPassword" required
              placeholder="Mínimo 6 caracteres"
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpPasswordValidation" class="mt-1 text-xs hidden"></div>
          </div>

          <!-- Confirmação de Senha -->
          <div>
            <label for="signUpConfirmPassword" class="block text-sm font-medium text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Confirme sua senha
            </label>
            <input type="password" id="signUpConfirmPassword" name="signUpConfirmPassword" required
              placeholder="Digite a senha novamente"
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpConfirmPasswordValidation" class="mt-1 text-xs hidden"></div>
          </div>

          <!-- Aceite dos Termos -->
          <div class="flex items-start space-x-2">
            <input type="checkbox" id="acceptTerms" name="acceptTerms" required
              class="mt-1 h-4 w-4 text-sky-600 focus:ring-sky-500 border-gray-300 rounded">
            <label for="acceptTerms" class="text-sm text-gray-700">
              Eu li e concordo com os
              <a href="/terms.html" target="_blank" class="text-sky-600 hover:text-sky-800 hover:underline font-medium">Termos de Serviço</a>
              e
              <a href="/privacy.html" target="_blank" class="text-sky-600 hover:text-sky-800 hover:underline font-medium">Política de Privacidade</a>.
            </label>
          </div>

          <button type="submit" id="signUpSubmitButton"
            class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">
            Criar Conta
          </button>

          <p class="text-xs text-center text-gray-500">
            Já tem uma conta?
            <button type="button" id="showLogin" class="text-sky-600 hover:underline font-medium">Entrar</button>
          </p>

          <button type="button" id="backToMainFromSignUp"
            class="w-full mt-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">
            Voltar
          </button>
        </form>
      </div>

      <!-- Botões de Ação de Autenticação -->
      <div id="authActions" class="space-y-3">
         <button id="loginWithEmailButton" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm flex items-center justify-center space-x-2 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            <span>Entrar com Email</span>
        </button>
        <button id="googleSignInButton"
          class="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm flex items-center justify-center space-x-2 transition-colors">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"/><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"/><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"/><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"/></svg>
          <span>Entrar com Google</span>
        </button>
      </div>
    </section>

    <!-- Informações do Usuário Logado -->
    <div id="userInfo" class="hidden text-center mb-4">
        <p class="text-gray-700">Logado como: <span id="userEmailDisplay" class="font-semibold"></span></p>
        <div class="mt-2 space-x-4">
            <button id="showHistoryButton" class="text-sm text-blue-500 hover:underline font-medium">Meu Histórico</button>
            <button id="logoutButton" class="text-sm text-red-500 hover:underline font-medium">Logout</button>
        </div>
    </div>

    <!-- Seção Principal de Encurtamento -->
    <main id="mainContentSection">
      <div class="space-y-4">
        <div>
          <label for="longUrl" class="block text-sm font-medium text-gray-700 mb-1">Cole sua URL longa aqui:</label>
          <input type="url" id="longUrl" name="longUrl" placeholder="https://www.seusite.com"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-300 ease-in-out placeholder-gray-400 text-gray-800">
        </div>

        <!-- Campo para Código Curto Personalizado (visível apenas para usuários logados) -->
         <div id="customShortCodeSection" class="hidden">
            <label for="customShortCode" class="block text-sm font-medium text-gray-700 mb-1">Código Curto Personalizado (opcional):</label>
            <div class="flex items-center">
                <span class="px-3 py-3 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 rounded-l-md">encurt.ar/</span>
                <input type="text" id="customShortCode" name="customShortCode" placeholder="ex: minhamarca"
                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-300 ease-in-out placeholder-gray-400 text-gray-800">
            </div>
             <p class="text-xs text-gray-500 mt-1">Apenas letras minúsculas, números e hífens. Máx. 20 caracteres.</p>
        </div>

        <button id="shortenButton"
          class="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
          <span>Encurtar Agora</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
        <div id="loadingIndicator" class="hidden flex justify-center items-center py-2">
          <div class="loader"></div>
          <p class="ml-2 text-gray-600">Processando...</p>
        </div>
      </div>

      <div id="result" class="mt-6 bg-sky-50 p-4 rounded-lg shadow hidden">
        <p class="text-sm text-gray-700 mb-1">Sua URL encurtada:</p>
        <div class="flex items-center space-x-2">
          <a id="shortUrlText" class="text-sky-600 font-semibold text-lg break-all flex-grow hover:underline cursor-pointer" target="_blank" rel="noopener noreferrer"></a>
          <button id="copyButton" title="Copiar URL"
            class="p-2 rounded-md bg-sky-500 hover:bg-sky-600 text-white focus:outline-none focus:ring-2 focus:ring-sky-400 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
              stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
    </main>

    <!-- Seção de Histórico de URLs (visível apenas para usuários logados) -->
    <section id="historySection" class="hidden mt-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-700">Meu Histórico de URLs</h3>
          <button id="hideHistoryButton" class="text-sm text-gray-500 hover:text-gray-700">✕ Fechar</button>
        </div>
        <div id="historyContent" class="space-y-3">
          <div id="historyLoading" class="text-center py-4 text-gray-500">Carregando histórico...</div>
          <div id="historyEmpty" class="hidden text-center py-4 text-gray-500">Você ainda não criou nenhuma URL encurtada.</div>
          <div id="historyList" class="space-y-3"></div>
        </div>
      </div>
    </section>

    <!-- Seção de QR Code -->
    <section id="qrcodeSection" class="hidden mt-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-700">Gerador de QR Code</h3>
          <button id="hideQRCodeButton" class="text-sm text-gray-500 hover:text-gray-700">✕ Fechar</button>
        </div>

        <div class="space-y-4">
          <div>
            <label for="qrCodeUrl" class="block text-sm font-medium text-gray-700 mb-1">URL para gerar QR Code:</label>
            <input type="url" id="qrCodeUrl" name="qrCodeUrl" placeholder="https://www.exemplo.com ou encurt.ar/abc123"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-300 ease-in-out placeholder-gray-400 text-gray-800">
          </div>

          <button id="generateQRButton"
            class="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
            </svg>
            <span>Gerar QR Code</span>
          </button>

          <div id="qrCodeResult" class="hidden">
            <div class="text-center">
              <div id="qrcodeDiv" class="flex justify-center mb-4"></div>
              <button id="downloadQRButton" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-colors flex items-center justify-center space-x-2 mx-auto">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span>Baixar QR Code</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    </div>
  </div>

  <!-- Footer Global -->
  <footer class="bg-white/10 backdrop-blur-md border-t border-white/20 mt-auto">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Links de navegação centralizados (alinhados com o bloco central) -->
      <div class="text-center mb-3">
        <div class="flex flex-wrap justify-center gap-6 text-sm">
          <a href="/about.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Sobre</a>
          <a href="/contact.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Contato</a>
          <a href="/privacy.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Privacidade</a>
          <a href="/terms.html" class="text-white hover:text-sky-200 hover:underline transition-colors font-medium">Termos de Uso</a>
        </div>
      </div>


    </div>
  </footer>

  <!-- Modal de Recuperação de Senha -->
  <div id="forgotPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-700">Recuperar Senha</h3>
        <button id="closeForgotPasswordModal" class="text-gray-500 hover:text-gray-700 text-xl">✕</button>
      </div>
      <form id="forgotPasswordForm" class="space-y-4">
        <div>
          <label for="forgotPasswordEmail" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" id="forgotPasswordEmail" name="forgotPasswordEmail" required
            class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400"
            placeholder="Digite seu email">
          <div id="forgotPasswordEmailValidation" class="mt-1 text-xs hidden"></div>
        </div>
        <button type="submit"
          class="w-full bg-sky-500 hover:bg-sky-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">
          Enviar Link de Recuperação
        </button>
      </form>
    </div>
  </div>

  <!-- Scripts otimizados para performance -->
  <script defer src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

  <!-- Google Analytics será adicionado apenas em produção -->

  <!-- Service Worker desabilitado para desenvolvimento -->
  <script>
    // Limpar Service Worker existente durante desenvolvimento
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(let registration of registrations) {
          registration.unregister().then(function(boolean) {
            console.log('Service Worker removido:', boolean);
          });
        }
      });

      // Limpar cache
      if ('caches' in window) {
        caches.keys().then(function(names) {
          for (let name of names) {
            caches.delete(name);
            console.log('Cache removido:', name);
          }
        });
      }
    }

    // Service Worker desabilitado durante desenvolvimento
    // Para reativar em produção, descomente o código abaixo:
    /*
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').then(function(registration) {
        console.log('Service Worker registrado com sucesso:', registration.scope);
      }).catch(function(err) {
        console.log('Falha ao registrar Service Worker:', err);
      });
    }
    */
  </script>

  <script>
    // Cache busting dinâmico
    const timestamp = new Date().getTime();
    const scriptElement = document.createElement('script');
    scriptElement.type = 'module';
    scriptElement.src = `script.js?v=${timestamp}`;
    document.head.appendChild(scriptElement);

    // Forçar reload de CSS também
    const cssElement = document.querySelector('link[href*="style.css"]');
    if (cssElement) {
      cssElement.href = `style.css?v=${timestamp}`;
    }
  </script>
</body>
</html>
