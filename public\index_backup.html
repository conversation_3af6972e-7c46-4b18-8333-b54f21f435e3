<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>encurt.ar - Encurtador de Links Gratuito e Rápido</title>

  <!-- SEO Meta Tags -->
  <meta name="description" content="Encurte seus links gratuitamente com o encurt.ar! Transforme URLs longas em links curtos e compartilháveis. Rápido, seguro e fácil de usar. Crie sua conta e gerencie seus links.">
  <meta name="keywords" content="encurtador de links, URL curta, encurtar link, link shortener, encurt.ar, compartilhar links">
  <meta name="author" content="encurt.ar">
  <meta name="robots" content="index, follow">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://encurt.ar/">
  <meta property="og:title" content="encurt.ar - Encurtador de Links Gratuito">
  <meta property="og:description" content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro.">
  <meta property="og:image" content="https://encurt.ar/og-image.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://encurt.ar/">
  <meta property="twitter:title" content="encurt.ar - Encurtador de Links Gratuito">
  <meta property="twitter:description" content="Transforme URLs longas em links curtos e compartilháveis. Gratuito, rápido e seguro.">
  <meta property="twitter:image" content="https://encurt.ar/og-image.png">

  <!-- Google AdSense -->
  <meta name="google-adsense-account" content="ca-pub-****************">
  

  
  <!-- Preload recursos críticos -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.tailwindcss.com">

  <!-- Outros recursos -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="style.css">



</head>

<body
  class="bg-gradient-to-br from-sky-500 to-indigo-600 min-h-screen flex flex-col items-center justify-center p-4 selection:bg-sky-300 selection:text-sky-900">

  <div
    class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg">
    <header class="text-center mb-6 sm:mb-8">
      <h1
        class="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-indigo-700">
        encurt.<span class="text-sky-400">ar</span>
      </h1>
      <p class="text-gray-600 mt-2 text-sm sm:text-base">Seu novo encurtador de links favorito!</p>
    </header>

    <div id="messageContainer" class="mb-4 space-y-2">
      <!-- Mensagens de erro/sucesso serão inseridas aqui pelo JavaScript -->
    </div>

    <!-- Seção de Autenticação (Login/Cadastro) -->
    <section id="authSection" class="mb-6">
      <!-- Formulários de Login/Cadastro (inicialmente ocultos ou um deles visível) -->
      <div id="authForms" class="hidden">
        <!-- Formulário de Login -->
        <form id="loginForm" class="space-y-4 mb-4">
          <h3 class="text-lg font-semibold text-gray-700">Login</h3>
          <div>
            <label for="loginEmail" class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" id="loginEmail" name="loginEmail" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="loginEmailValidation" class="mt-1 text-xs hidden"></div>
          </div>
          <div>
            <label for="loginPassword" class="block text-sm font-medium text-gray-700">Senha</label>
            <input type="password" id="loginPassword" name="loginPassword" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500">
          </div>
          <button type="submit"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Entrar</button>
          <p class="text-xs text-center text-gray-500">Não tem conta? <button type="button" id="showSignUp" class="text-sky-600 hover:underline font-medium">Cadastre-se</button></p>
          <p class="text-xs text-center text-gray-500 mt-2"><button type="button" id="forgotPassword" class="text-sky-600 hover:underline font-medium">Esqueci minha senha</button></p>
          <button type="button" id="backToMainFromLogin" class="w-full mt-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Voltar</button>
        </form>

        <!-- Formulário de Cadastro -->
        <form id="signUpForm" class="space-y-4 mb-4 hidden">
          <h3 class="text-lg font-semibold text-gray-700">Cadastro</h3>
          <div>
            <label for="signUpEmail" class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" id="signUpEmail" name="signUpEmail" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400">
            <div id="signUpEmailValidation" class="mt-1 text-xs hidden"></div>
          </div>
          <div>
            <label for="signUpPassword" class="block text-sm font-medium text-gray-700">Senha (mínimo 6 caracteres)</label>
            <input type="password" id="signUpPassword" name="signUpPassword" required
              class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500">
          </div>
          <button type="submit"
            class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Cadastrar</button>
          <p class="text-xs text-center text-gray-500">Já tem conta? <button type="button" id="showLogin" class="text-sky-600 hover:underline font-medium">Faça Login</button></p>
          <button type="button" id="backToMainFromSignUp" class="w-full mt-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">Voltar</button>
        </form>
      </div>

      <!-- Botões de Ação de Autenticação -->
      <div id="authActions" class="space-y-3">
         <button id="loginWithEmailButton" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm flex items-center justify-center space-x-2 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            <span>Entrar com Email</span>
        </button>
        <button id="googleSignInButton"
          class="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm flex items-center justify-center space-x-2 transition-colors">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"/><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"/><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"/><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"/></svg>
          <span>Entrar com Google</span>
        </button>
      </div>
    </section>
    
    <!-- Informações do Usuário Logado -->
    <div id="userInfo" class="hidden text-center mb-4">
        <p class="text-gray-700">Logado como: <span id="userEmailDisplay" class="font-semibold"></span></p>
        <div class="mt-2 space-x-4">
            <button id="showHistoryButton" class="text-sm text-blue-500 hover:underline font-medium">Meu Histórico</button>
            <button id="logoutButton" class="text-sm text-red-500 hover:underline font-medium">Logout</button>
        </div>
    </div>

    <!-- Seção Principal de Encurtamento -->
    <main id="mainContentSection">
      <div class="space-y-4">
        <div>
          <label for="longUrl" class="block text-sm font-medium text-gray-700 mb-1">Cole sua URL longa aqui:</label>
          <input type="url" id="longUrl" name="longUrl" placeholder="https://www.seusite.com"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-300 ease-in-out placeholder-gray-400 text-gray-800">
        </div>
        
        <!-- Campo para Código Curto Personalizado (visível apenas para usuários logados) -->
         <div id="customShortCodeSection" class="hidden">
            <label for="customShortCode" class="block text-sm font-medium text-gray-700 mb-1">Código Curto Personalizado (opcional):</label>
            <div class="flex items-center">
                <span class="px-3 py-3 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 rounded-l-md">encurt.ar/</span>
                <input type="text" id="customShortCode" name="customShortCode" placeholder="ex: minhamarca"
                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-300 ease-in-out placeholder-gray-400 text-gray-800">
            </div>
             <p class="text-xs text-gray-500 mt-1">Apenas letras minúsculas, números e hífens. Máx. 20 caracteres.</p>
        </div>

        <button id="shortenButton"
          class="w-full bg-gradient-to-r from-sky-500 to-indigo-600 hover:from-sky-600 hover:to-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
          <span>Encurtar Agora</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
        <div id="loadingIndicator" class="hidden flex justify-center items-center py-2">
          <div class="loader"></div>
          <p class="ml-2 text-gray-600">Processando...</p>
        </div>
      </div>

      <div id="result" class="mt-6 bg-sky-50 p-4 rounded-lg shadow hidden">
        <p class="text-sm text-gray-700 mb-1">Sua URL encurtada:</p>
        <div class="flex items-center space-x-2">
          <a id="shortUrlText" class="text-sky-600 font-semibold text-lg break-all flex-grow hover:underline cursor-pointer" target="_blank" rel="noopener noreferrer"></a>
          <button id="copyButton" title="Copiar URL"
            class="p-2 rounded-md bg-sky-500 hover:bg-sky-600 text-white focus:outline-none focus:ring-2 focus:ring-sky-400 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
              stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
    </main>

    <!-- Seção de Histórico de URLs (visível apenas para usuários logados) -->
    <section id="historySection" class="hidden mt-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-700">Meu Histórico de URLs</h3>
          <button id="hideHistoryButton" class="text-sm text-gray-500 hover:text-gray-700">✕ Fechar</button>
        </div>
        <div id="historyContent" class="space-y-3">
          <div id="historyLoading" class="text-center py-4 text-gray-500">Carregando histórico...</div>
          <div id="historyEmpty" class="hidden text-center py-4 text-gray-500">Você ainda não criou nenhuma URL encurtada.</div>
          <div id="historyList" class="space-y-3"></div>
        </div>
      </div>
    </section>

    <footer class="text-center mt-8 text-xs text-gray-500">
      <div class="mb-3 space-y-2">
        <div class="flex flex-wrap justify-center gap-4 text-xs">
          <a href="/about.html" class="text-sky-600 hover:text-sky-800 hover:underline transition-colors">Sobre</a>
          <a href="/contact.html" class="text-sky-600 hover:text-sky-800 hover:underline transition-colors">Contato</a>
          <a href="/privacy.html" class="text-sky-600 hover:text-sky-800 hover:underline transition-colors">Privacidade</a>
          <a href="/terms.html" class="text-sky-600 hover:text-sky-800 hover:underline transition-colors">Termos de Uso</a>
        </div>
        <div class="text-xs text-gray-400">
          <p>Hospedado com ❤️ no Google Firebase</p>
        </div>
      </div>
      <p>&copy; <span id="currentYear">2024</span> Encurt.ar - Todos os direitos reservados.</p>
      <p id="authStatusDisplay" class="mt-1 font-mono">Carregando status de autenticação...</p>
    </footer>
  </div>

  <!-- Modal de Recuperação de Senha -->
  <div id="forgotPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-700">Recuperar Senha</h3>
        <button id="closeForgotPasswordModal" class="text-gray-500 hover:text-gray-700 text-xl">✕</button>
      </div>
      <form id="forgotPasswordForm" class="space-y-4">
        <div>
          <label for="forgotPasswordEmail" class="block text-sm font-medium text-gray-700">Email</label>
          <input type="email" id="forgotPasswordEmail" name="forgotPasswordEmail" required
            class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 placeholder-gray-400"
            placeholder="Digite seu email">
          <div id="forgotPasswordEmailValidation" class="mt-1 text-xs hidden"></div>
        </div>
        <button type="submit"
          class="w-full bg-sky-500 hover:bg-sky-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition-colors">
          Enviar Link de Recuperação
        </button>
      </form>
    </div>
  </div>

  <!-- Scripts otimizados para performance -->
  <script defer src="https://cdn.tailwindcss.com"></script>

  <!-- Google Analytics (carregamento otimizado) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-L12DSJ4R7D"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-L12DSJ4R7D');
  </script>

  <script type="module" src="script.js"></script>
</body>
</html>
