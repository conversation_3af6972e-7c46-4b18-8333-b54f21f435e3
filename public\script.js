// Importações do Firebase
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
import {
    getAuth,
    signInAnonymously,
    onAuthStateChanged,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    GoogleAuthProvider,
    signInWithPopup,
    signOut,
    sendPasswordResetEmail
} from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
import { getFirestore, doc, setDoc, getDoc, serverTimestamp, collection, query, where, orderBy, getDocs, deleteDoc } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

// --- Configuração do Firebase ---
// Prioriza a configuração do ambiente Canvas (__firebase_config)
// Se não disponível, usa a configuração hardcoded do seu script original.
let activeFirebaseConfig;
const firebaseConfigJSONFromEnv = typeof __firebase_config !== 'undefined' ? __firebase_config : null;

const localHardcodedConfig = { // Nova configuração do Firebase
    apiKey: "AIzaSyAkJ1C3NXyzAfJveWiQJVYr-OAISg_Jfh8",
    authDomain: "projeto-encurtar.firebaseapp.com",
    projectId: "projeto-encurtar",
    storageBucket: "projeto-encurtar.firebasestorage.app",
    messagingSenderId: "638723124202",
    appId: "1:638723124202:web:65c4306e94aae917fb6093",
    measurementId: "G-HKYKRNJTJ6"
};

// Elementos da UI (definidos cedo para que displayMessage possa usá-los)
const messageContainer = document.getElementById('messageContainer');

if (firebaseConfigJSONFromEnv) {
    try {
        activeFirebaseConfig = JSON.parse(firebaseConfigJSONFromEnv);
        console.log("Usando configuração Firebase do ambiente Canvas.");
        if (!activeFirebaseConfig.apiKey) {
             console.warn("API Key ausente na configuração do Firebase do ambiente. Autenticação falhará.");
             if (messageContainer) displayMessage("API Key do Firebase ausente. Autenticação não funcionará.", true);
        }
    } catch (parseError) {
        console.error("Erro ao parsear __firebase_config, usando configuração local hardcoded:", parseError);
        activeFirebaseConfig = localHardcodedConfig;
        if (messageContainer) displayMessage("Erro na configuração do Firebase do ambiente, usando config local.", true);
    }
} else {
    console.log("Firebase SDK inicializado com configuração local.");
    activeFirebaseConfig = localHardcodedConfig;
}

// --- Inicialização do Firebase ---
let app, db, auth, googleProvider;
try {
    app = initializeApp(activeFirebaseConfig);

    // Conectar ao banco padrão
    db = getFirestore(app);

    auth = getAuth(app);
    googleProvider = new GoogleAuthProvider();
    console.log("Firebase SDK inicializado - usando banco: bd-siteencurtar");
} catch (e) {
    console.error("Erro crítico ao inicializar Firebase SDK: ", e.message, e.stack);
    if (messageContainer) displayMessage(`Erro fatal ao inicializar Firebase: ${e.message}. A aplicação pode não funcionar. Verifique a configuração.`, true);
}

let currentUserId = null;
let isUserAnonymous = true;

// Elementos da UI (restantes)
const longUrlInput = document.getElementById('longUrl');
const shortenButton = document.getElementById('shortenButton');
const resultDiv = document.getElementById('result');
const shortUrlText = document.getElementById('shortUrlText');
const copyButton = document.getElementById('copyButton');
const loadingIndicator = document.getElementById('loadingIndicator');
const customShortCodeInput = document.getElementById('customShortCode');
const customShortCodeSection = document.getElementById('customShortCodeSection');
const mainContentSection = document.getElementById('mainContentSection');

// Elementos de Autenticação
const authSection = document.getElementById('authSection');
const authForms = document.getElementById('authForms');
const authActions = document.getElementById('authActions');
const loginForm = document.getElementById('loginForm');
const signUpForm = document.getElementById('signUpForm');
const loginEmailInput = document.getElementById('loginEmail');
const loginPasswordInput = document.getElementById('loginPassword');
const signUpFullNameInput = document.getElementById('signUpFullName');
const signUpUsernameInput = document.getElementById('signUpUsername');
const signUpEmailInput = document.getElementById('signUpEmail');
const signUpPasswordInput = document.getElementById('signUpPassword');
const signUpConfirmPasswordInput = document.getElementById('signUpConfirmPassword');
const acceptTermsInput = document.getElementById('acceptTerms');
const signUpSubmitButton = document.getElementById('signUpSubmitButton');
const googleSignInButton = document.getElementById('googleSignInButton');
const loginWithEmailButton = document.getElementById('loginWithEmailButton');
const showSignUpButton = document.getElementById('showSignUp');
const showLoginButton = document.getElementById('showLogin');
const backToMainFromLoginButton = document.getElementById('backToMainFromLogin');
const backToMainFromSignUpButton = document.getElementById('backToMainFromSignUp');

// Elementos de validação
const loginEmailValidation = document.getElementById('loginEmailValidation');
const signUpFullNameValidation = document.getElementById('signUpFullNameValidation');
const signUpUsernameValidation = document.getElementById('signUpUsernameValidation');
const signUpEmailValidation = document.getElementById('signUpEmailValidation');
const signUpPasswordValidation = document.getElementById('signUpPasswordValidation');
const signUpConfirmPasswordValidation = document.getElementById('signUpConfirmPasswordValidation');

// Elementos de recuperação de senha
const forgotPasswordButton = document.getElementById('forgotPassword');
const forgotPasswordModal = document.getElementById('forgotPasswordModal');
const closeForgotPasswordModal = document.getElementById('closeForgotPasswordModal');
const forgotPasswordForm = document.getElementById('forgotPasswordForm');
const forgotPasswordEmailInput = document.getElementById('forgotPasswordEmail');
const forgotPasswordEmailValidation = document.getElementById('forgotPasswordEmailValidation');

// Elementos de histórico
const showHistoryButton = document.getElementById('showHistoryButton');
const hideHistoryButton = document.getElementById('hideHistoryButton');
const historySection = document.getElementById('historySection');
const historyLoading = document.getElementById('historyLoading');
const historyEmpty = document.getElementById('historyEmpty');
const historyList = document.getElementById('historyList');

const userInfoDiv = document.getElementById('userInfo');
const userEmailDisplay = document.getElementById('userEmailDisplay');
const logoutButton = document.getElementById('logoutButton');
const authStatusDisplay = document.getElementById('authStatusDisplay'); // Elemento do rodapé para status

// Elementos de dark mode e funcionalidades avançadas
const darkModeToggle = document.getElementById('darkModeToggle');
const qrcodeDiv = document.getElementById('qrcodeDiv');
const downloadQRButton = document.getElementById('downloadQRButton');
const previewButton = document.getElementById('previewButton');
const exportHistoryButton = document.getElementById('exportHistoryButton');
const bulkShortenerToggle = document.getElementById('bulkShortenerToggle');
const advancedOptions = document.getElementById('advancedOptions');
const enableExpiration = document.getElementById('enableExpiration');
const expirationDate = document.getElementById('expirationDate');
const enablePassword = document.getElementById('enablePassword');
const linkPassword = document.getElementById('linkPassword');

// Elementos de analytics
const totalUrlsCount = document.getElementById('totalUrlsCount');
const totalClicksCount = document.getElementById('totalClicksCount');
const avgClicksCount = document.getElementById('avgClicksCount');
const analyticsChart = document.getElementById('analyticsChart');
const clicksChart = document.getElementById('clicksChart');

// Função para exibir mensagens
function displayMessage(message, isError = false) {
    if (!messageContainer) {
        console.warn("Message container não encontrado ao tentar exibir: ", message);
        return;
    }
    messageContainer.innerHTML = '';
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.className = `p-3 rounded-md text-sm ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`;
    messageContainer.appendChild(messageDiv);
    setTimeout(() => {
        if (messageContainer) messageContainer.innerHTML = '';
    }, 5000);
}

// Função para validar email em tempo real
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Função para validar URL de forma mais robusta
function validateUrl(url) {
    // Remove espaços em branco
    url = url.trim();

    // Verifica se não está vazio
    if (!url) {
        return false;
    }

    // Adiciona protocolo se não tiver
    let testUrl = url;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
        testUrl = 'https://' + testUrl;
    }

    try {
        // Usa a API nativa do navegador para validar
        const urlObj = new URL(testUrl);

        // Verifica se tem hostname válido
        if (!urlObj.hostname || urlObj.hostname.length < 3) {
            return false;
        }

        // Verifica se tem pelo menos um ponto no hostname (domínio.tld)
        if (!urlObj.hostname.includes('.')) {
            return false;
        }

        // Verifica se o hostname não começa ou termina com ponto
        if (urlObj.hostname.startsWith('.') || urlObj.hostname.endsWith('.')) {
            return false;
        }

        // Verifica se não tem pontos consecutivos
        if (urlObj.hostname.includes('..')) {
            return false;
        }

        // Divide o hostname em partes
        const parts = urlObj.hostname.split('.');

        // Deve ter pelo menos 2 partes (domínio.tld)
        if (parts.length < 2) {
            return false;
        }

        // Verifica cada parte do domínio
        for (const part of parts) {
            // Cada parte deve ter pelo menos 1 caractere
            if (part.length === 0) {
                return false;
            }

            // Cada parte deve começar e terminar com alfanumérico
            if (!/^[a-zA-Z0-9]/.test(part) || !/[a-zA-Z0-9]$/.test(part)) {
                return false;
            }

            // Cada parte pode conter apenas letras, números e hífens
            if (!/^[a-zA-Z0-9-]+$/.test(part)) {
                return false;
            }
        }

        // A última parte (TLD) deve ter pelo menos 2 caracteres e ser apenas letras
        const tld = parts[parts.length - 1];
        if (tld.length < 2 || !/^[a-zA-Z]+$/.test(tld)) {
            return false;
        }

        return true;
    } catch (e) {
        return false;
    }
}

function showEmailValidation(element, isValid, message) {
    if (!element) return;

    element.classList.remove('hidden');
    if (isValid) {
        element.className = 'mt-1 text-xs text-green-600';
        element.textContent = '✓ Email válido';
    } else {
        element.className = 'mt-1 text-xs text-red-600';
        element.textContent = message || '✗ Formato de email inválido';
    }
}

function hideEmailValidation(element) {
    if (element) {
        element.classList.add('hidden');
    }
}

// Função para validar nome completo
function validateFullName(name) {
    return name.trim().length >= 2 && /^[a-zA-ZÀ-ÿ\s]+$/.test(name.trim());
}

// Função para validar nome de usuário
function validateUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
}

// Função para validar senha
function validatePassword(password) {
    return password.length >= 6;
}

// Função para mostrar validação de campo
function showFieldValidation(element, isValid, message) {
    if (!element) return;

    element.classList.remove('hidden');
    if (isValid) {
        element.className = 'mt-1 text-xs text-green-600';
        element.textContent = '✓ ' + message;
    } else {
        element.className = 'mt-1 text-xs text-red-600';
        element.textContent = '✗ ' + message;
    }
}

// Função para esconder validação de campo
function hideFieldValidation(element) {
    if (element) {
        element.classList.add('hidden');
    }
}

// --- Funcionalidades de Dark Mode ---
function initDarkMode() {
    // Verifica preferência salva ou preferência do sistema
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add('dark');
        updateDarkModeIcon(true);
    } else {
        document.documentElement.classList.remove('dark');
        updateDarkModeIcon(false);
    }
}

function toggleDarkMode() {
    const isDark = document.documentElement.classList.toggle('dark');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
    updateDarkModeIcon(isDark);
}

function updateDarkModeIcon(isDark) {
    if (!darkModeToggle) return;

    const sunIcon = darkModeToggle.querySelector('.sun');
    const moonIcon = darkModeToggle.querySelector('.moon');

    if (isDark) {
        sunIcon?.classList.remove('hidden');
        moonIcon?.classList.add('hidden');
    } else {
        sunIcon?.classList.add('hidden');
        moonIcon?.classList.remove('hidden');
    }
}

// --- Funcionalidades de QR Code ---
function generateQRCode(url) {
    if (!qrcodeDiv || typeof QRCode === 'undefined') {
        console.warn('QRCode library não carregada ou elemento não encontrado');
        return;
    }

    // Limpa QR code anterior
    qrcodeDiv.innerHTML = '';

    try {
        QRCode.toCanvas(qrcodeDiv, url, {
            width: 150,
            height: 150,
            margin: 2,
            color: {
                dark: document.documentElement.classList.contains('dark') ? '#ffffff' : '#000000',
                light: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff'
            }
        }, function (error) {
            if (error) {
                console.error('Erro ao gerar QR Code:', error);
                qrcodeDiv.innerHTML = '<p class="text-red-500 text-sm">Erro ao gerar QR Code</p>';
            }
        });
    } catch (error) {
        console.error('Erro ao gerar QR Code:', error);
        qrcodeDiv.innerHTML = '<p class="text-red-500 text-sm">QR Code indisponível</p>';
    }
}

function downloadQRCode() {
    const canvas = qrcodeDiv.querySelector('canvas');
    if (!canvas) {
        displayMessage('QR Code não encontrado para download.', true);
        return;
    }

    try {
        const link = document.createElement('a');
        link.download = 'qrcode-encurt-ar.png';
        link.href = canvas.toDataURL();
        link.click();
        displayMessage('QR Code baixado com sucesso!', false);
    } catch (error) {
        console.error('Erro ao baixar QR Code:', error);
        displayMessage('Erro ao baixar QR Code.', true);
    }
}

// --- Funcionalidades de Analytics ---
function updateStatsOverview(urls) {
    if (!urls || !Array.isArray(urls)) return;

    const totalUrls = urls.length;
    const totalClicks = urls.reduce((sum, url) => sum + (url.clickCount || 0), 0);
    const avgClicks = totalUrls > 0 ? Math.round(totalClicks / totalUrls) : 0;

    if (totalUrlsCount) totalUrlsCount.textContent = totalUrls;
    if (totalClicksCount) totalClicksCount.textContent = totalClicks;
    if (avgClicksCount) avgClicksCount.textContent = avgClicks;
}

function createClicksChart(urls) {
    if (!clicksChart || typeof Chart === 'undefined' || !urls || !Array.isArray(urls)) {
        return;
    }

    // Preparar dados dos últimos 7 dias
    const last7Days = [];
    const clicksData = [];

    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        last7Days.push(dateStr);

        // Simular dados de cliques por dia (em uma implementação real, isso viria do backend)
        const dayClicks = Math.floor(Math.random() * 50);
        clicksData.push(dayClicks);
    }

    const ctx = clicksChart.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: last7Days,
            datasets: [{
                label: 'Cliques',
                data: clicksData,
                borderColor: '#0ea5e9',
                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    },
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    }
                },
                x: {
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    },
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    }
                }
            }
        }
    });

    if (analyticsChart) {
        analyticsChart.classList.remove('hidden');
    }
}

// --- Funcionalidades de Exportação ---
function exportHistoryToCSV(urls) {
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
        displayMessage('Nenhum dado para exportar.', true);
        return;
    }

    // Cabeçalhos do CSV
    const headers = ['URL Encurtada', 'URL Original', 'Data de Criação', 'Cliques', 'Último Acesso', 'Código Personalizado'];

    // Converter dados para CSV
    const csvContent = [
        headers.join(','),
        ...urls.map(url => [
            `"https://encurt.ar/${url.shortCode}"`,
            `"${url.longUrl}"`,
            `"${url.createdAt ? new Date(url.createdAt.seconds * 1000).toLocaleString('pt-BR') : 'N/A'}"`,
            url.clickCount || 0,
            `"${url.lastAccessed ? new Date(url.lastAccessed.seconds * 1000).toLocaleString('pt-BR') : 'Nunca'}"`,
            url.isCustom ? 'Sim' : 'Não'
        ].join(','))
    ].join('\n');

    // Criar e baixar arquivo
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `historico-encurt-ar-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    displayMessage('Histórico exportado com sucesso!', false);
}

// Atualiza a UI com base no estado de autenticação
function updateUIForAuthState(user) {
    if (user && !user.isAnonymous) {
        isUserAnonymous = false;
        currentUserId = user.uid;
        if (authSection) authSection.classList.add('hidden');
        if (authForms) authForms.classList.add('hidden');
        if (userInfoDiv) userInfoDiv.classList.remove('hidden');
        if (userEmailDisplay) userEmailDisplay.textContent = user.email || user.displayName || 'Usuário Logado';
        if (authStatusDisplay) authStatusDisplay.textContent = `Logado como: ${user.email || user.displayName || user.uid.substring(0,8) + "..."}`;
        if (customShortCodeSection) customShortCodeSection.classList.remove('hidden');
        if (advancedOptions) advancedOptions.classList.remove('hidden');
        if (bulkShortenerToggle) bulkShortenerToggle.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    } else {
        isUserAnonymous = true;
        if (user) { // Usuário anônimo
             currentUserId = user.uid;
             if (authStatusDisplay) authStatusDisplay.textContent = `ID Anônimo: ${user.uid.substring(0,8)}... (Recurso de código personalizado desabilitado)`;
        } else { // Usuário deslogado (null)
            currentUserId = null;
            if (authStatusDisplay) authStatusDisplay.textContent = "Você não está logado. Faça login para usar códigos personalizados.";
        }

        if (authSection) authSection.classList.remove('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (authForms) authForms.classList.add('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        if (signUpForm) signUpForm.classList.add('hidden');
        if (userInfoDiv) userInfoDiv.classList.add('hidden');
        if (customShortCodeSection) customShortCodeSection.classList.add('hidden');
        if (advancedOptions) advancedOptions.classList.add('hidden');
        if (bulkShortenerToggle) bulkShortenerToggle.classList.add('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    }
}

// Observador de estado de autenticação
if (auth) {
    onAuthStateChanged(auth, async (user) => {
        if (user) {
            updateUIForAuthState(user);
        } else {
            try {
                const userCredential = await signInAnonymously(auth);
                console.log("Usuário anônimo para funcionalidades básicas:", userCredential.user.uid);
                updateUIForAuthState(userCredential.user);
            } catch (error) {
                console.error("Erro na autenticação anônima:", error);
                displayMessage("Erro de autenticação. Verifique se a API Key do Firebase é válida.", true);
                updateUIForAuthState(null);
            }
        }
    });
} else {
    console.error("Firebase Auth não foi inicializado. A autenticação não funcionará.");
    updateUIForAuthState(null);
    displayMessage("Erro na configuração do Firebase. A autenticação está desabilitada.", true);
}

// --- Validação de URL em Tempo Real ---
if (longUrlInput) {
    // Criar elemento de validação para URL
    const urlValidation = document.createElement('div');
    urlValidation.className = 'mt-1 text-xs hidden';
    urlValidation.id = 'urlValidation';
    longUrlInput.parentNode.insertBefore(urlValidation, longUrlInput.nextSibling);

    longUrlInput.addEventListener('input', (e) => {
        const url = e.target.value.trim();
        if (url.length === 0) {
            urlValidation.classList.add('hidden');
        } else if (validateUrl(url)) {
            urlValidation.className = 'mt-1 text-xs text-green-600';
            urlValidation.textContent = '✓ URL válida';
            urlValidation.classList.remove('hidden');
        } else {
            urlValidation.className = 'mt-1 text-xs text-red-600';
            urlValidation.textContent = '✗ Insira uma URL válida (ex: https://www.google.com)';
            urlValidation.classList.remove('hidden');
        }
    });
}

// --- Event Listeners para Dark Mode ---
// Event listener para dark mode toggle será adicionado no DOMContentLoaded

// Event listeners para opções avançadas
if (enableExpiration) {
    enableExpiration.addEventListener('change', (e) => {
        if (expirationDate) {
            if (e.target.checked) {
                expirationDate.classList.remove('hidden');
                // Definir data mínima como agora
                const now = new Date();
                now.setMinutes(now.getMinutes() + 30); // Mínimo 30 minutos
                expirationDate.min = now.toISOString().slice(0, 16);
            } else {
                expirationDate.classList.add('hidden');
                expirationDate.value = '';
            }
        }
    });
}

if (enablePassword) {
    enablePassword.addEventListener('change', (e) => {
        if (linkPassword) {
            if (e.target.checked) {
                linkPassword.classList.remove('hidden');
                linkPassword.required = true;
            } else {
                linkPassword.classList.add('hidden');
                linkPassword.required = false;
                linkPassword.value = '';
            }
        }
    });
}

// Event listeners para QR code
if (downloadQRButton) {
    downloadQRButton.addEventListener('click', downloadQRCode);
}

if (previewButton) {
    previewButton.addEventListener('click', () => {
        const shortUrl = shortUrlText?.href;
        if (shortUrl) {
            window.open(shortUrl, '_blank');
        } else {
            displayMessage('Nenhuma URL para visualizar.', true);
        }
    });
}

// Event listener para exportação de histórico
if (exportHistoryButton) {
    exportHistoryButton.addEventListener('click', async () => {
        if (!currentUserId || isUserAnonymous) {
            displayMessage('Faça login para exportar seu histórico.', true);
            return;
        }

        try {
            const urls = await loadUserHistory();
            exportHistoryToCSV(urls);
        } catch (error) {
            console.error('Erro ao exportar histórico:', error);
            displayMessage('Erro ao exportar histórico.', true);
        }
    });
}

// --- Validação de Email em Tempo Real ---
if (loginEmailInput) {
    loginEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(loginEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(loginEmailValidation, true);
        } else {
            showEmailValidation(loginEmailValidation, false);
        }
    });
}

if (signUpEmailInput) {
    signUpEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(signUpEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(signUpEmailValidation, true);
        } else {
            showEmailValidation(signUpEmailValidation, false);
        }
    });
}

if (forgotPasswordEmailInput) {
    forgotPasswordEmailInput.addEventListener('input', (e) => {
        const email = e.target.value.trim();
        if (email.length === 0) {
            hideEmailValidation(forgotPasswordEmailValidation);
        } else if (validateEmail(email)) {
            showEmailValidation(forgotPasswordEmailValidation, true);
        } else {
            showEmailValidation(forgotPasswordEmailValidation, false);
        }
    });
}

// --- Validações em Tempo Real para Cadastro ---
if (signUpFullNameInput) {
    signUpFullNameInput.addEventListener('input', (e) => {
        const fullName = e.target.value.trim();
        if (fullName.length === 0) {
            hideFieldValidation(signUpFullNameValidation);
        } else if (validateFullName(fullName)) {
            showFieldValidation(signUpFullNameValidation, true, "Nome válido");
        } else {
            showFieldValidation(signUpFullNameValidation, false, "Nome deve ter pelo menos 2 caracteres e conter apenas letras");
        }
    });
}

if (signUpUsernameInput) {
    signUpUsernameInput.addEventListener('input', (e) => {
        const username = e.target.value.trim();
        if (username.length === 0) {
            hideFieldValidation(signUpUsernameValidation);
        } else if (validateUsername(username)) {
            showFieldValidation(signUpUsernameValidation, true, "Nome de usuário válido");
        } else {
            showFieldValidation(signUpUsernameValidation, false, "3-20 caracteres (letras, números, underscore)");
        }
    });
}

if (signUpPasswordInput) {
    signUpPasswordInput.addEventListener('input', (e) => {
        const password = e.target.value;
        if (password.length === 0) {
            hideFieldValidation(signUpPasswordValidation);
        } else if (validatePassword(password)) {
            showFieldValidation(signUpPasswordValidation, true, "Senha válida");
        } else {
            showFieldValidation(signUpPasswordValidation, false, "Mínimo 6 caracteres");
        }

        // Também validar confirmação de senha se ela já foi preenchida
        if (signUpConfirmPasswordInput && signUpConfirmPasswordInput.value.length > 0) {
            const confirmPassword = signUpConfirmPasswordInput.value;
            if (password === confirmPassword) {
                showFieldValidation(signUpConfirmPasswordValidation, true, "Senhas coincidem");
            } else {
                showFieldValidation(signUpConfirmPasswordValidation, false, "Senhas não coincidem");
            }
        }
    });
}

if (signUpConfirmPasswordInput) {
    signUpConfirmPasswordInput.addEventListener('input', (e) => {
        const confirmPassword = e.target.value;
        const password = signUpPasswordInput ? signUpPasswordInput.value : '';

        if (confirmPassword.length === 0) {
            hideFieldValidation(signUpConfirmPasswordValidation);
        } else if (password === confirmPassword) {
            showFieldValidation(signUpConfirmPasswordValidation, true, "Senhas coincidem");
        } else {
            showFieldValidation(signUpConfirmPasswordValidation, false, "Senhas não coincidem");
        }
    });
}

// --- Manipuladores de Eventos de Autenticação ---
if (loginWithEmailButton) {
    loginWithEmailButton.addEventListener('click', () => {
        if (authActions) authActions.classList.add('hidden');
        if (authForms) authForms.classList.remove('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        if (signUpForm) signUpForm.classList.add('hidden');
        if (mainContentSection) mainContentSection.classList.add('hidden');
    });
}

if (showSignUpButton) {
    showSignUpButton.addEventListener('click', (e) => {
        e.preventDefault();
        if (loginForm) loginForm.classList.add('hidden');
        if (signUpForm) signUpForm.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.add('hidden');
    });
}

if (showLoginButton) {
    showLoginButton.addEventListener('click', (e) => {
        e.preventDefault();
        if (signUpForm) signUpForm.classList.add('hidden');
        if (loginForm) loginForm.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.add('hidden');
    });
}

if (backToMainFromLoginButton) {
    backToMainFromLoginButton.addEventListener('click', () => {
        if (authForms) authForms.classList.add('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    });
}

if (backToMainFromSignUpButton) {
    backToMainFromSignUpButton.addEventListener('click', () => {
        if (authForms) authForms.classList.add('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');
    });
}

// --- Recuperação de Senha ---
if (forgotPasswordButton) {
    forgotPasswordButton.addEventListener('click', () => {
        if (forgotPasswordModal) forgotPasswordModal.classList.remove('hidden');
    });
}

if (closeForgotPasswordModal) {
    closeForgotPasswordModal.addEventListener('click', () => {
        if (forgotPasswordModal) forgotPasswordModal.classList.add('hidden');
        if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
        hideEmailValidation(forgotPasswordEmailValidation);
    });
}

// Fechar modal clicando fora dele
if (forgotPasswordModal) {
    forgotPasswordModal.addEventListener('click', (e) => {
        if (e.target === forgotPasswordModal) {
            forgotPasswordModal.classList.add('hidden');
            if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
            hideEmailValidation(forgotPasswordEmailValidation);
        }
    });
}

if (forgotPasswordForm) {
    forgotPasswordForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) {
            displayMessage("Firebase Auth não inicializado.", true);
            return;
        }

        const email = forgotPasswordEmailInput.value.trim();
        if (!email) {
            displayMessage("Por favor, digite seu email.", true);
            return;
        }

        if (!validateEmail(email)) {
            displayMessage("Por favor, digite um email válido.", true);
            return;
        }

        try {
            await sendPasswordResetEmail(auth, email);
            displayMessage("Link de recuperação enviado para seu email!", false);
            if (forgotPasswordModal) forgotPasswordModal.classList.add('hidden');
            if (forgotPasswordEmailInput) forgotPasswordEmailInput.value = '';
            hideEmailValidation(forgotPasswordEmailValidation);
        } catch (error) {
            console.error("Erro ao enviar email de recuperação:", error);
            let errorMessage = "Erro ao enviar email de recuperação.";

            if (error.code === 'auth/user-not-found') {
                errorMessage = "Nenhuma conta encontrada com este email.";
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = "Email inválido.";
            } else if (error.code === 'auth/too-many-requests') {
                errorMessage = "Muitas tentativas. Tente novamente mais tarde.";
            }

            displayMessage(errorMessage, true);
        }
    });
}

if (loginForm) {
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }
        const email = loginEmailInput.value;
        const password = loginPasswordInput.value;
        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        try {
            await signInWithEmailAndPassword(auth, email, password);
            displayMessage("Login bem-sucedido!", false);
        } catch (error) {
            console.error("Erro no login:", error);
            let errorMessage = `Erro no login: ${error.message}`;

            // Tratamento específico para erro de operação não permitida
            if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Erro no login: Firebase: Error (auth/operation-not-allowed). " +
                              "O login por e-mail/senha não está habilitado no projeto Firebase. " +
                              "Para resolver: 1) Acesse o Firebase Console, 2) Vá em Authentication > Sign-in method, " +
                              "3) Habilite 'Email/Password' como provedor de login.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
        }
    });
}

if (signUpForm) {
    signUpForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }

        // Obter valores dos campos
        const fullName = signUpFullNameInput.value.trim();
        const username = signUpUsernameInput.value.trim();
        const email = signUpEmailInput.value.trim();
        const password = signUpPasswordInput.value;
        const confirmPassword = signUpConfirmPasswordInput.value;
        const acceptTerms = acceptTermsInput.checked;

        // Validações
        let hasErrors = false;

        // Validar nome completo
        if (!validateFullName(fullName)) {
            showFieldValidation(signUpFullNameValidation, false, "Nome deve ter pelo menos 2 caracteres e conter apenas letras");
            hasErrors = true;
        } else {
            showFieldValidation(signUpFullNameValidation, true, "Nome válido");
        }

        // Validar nome de usuário
        if (!validateUsername(username)) {
            showFieldValidation(signUpUsernameValidation, false, "Nome de usuário deve ter 3-20 caracteres (letras, números, underscore)");
            hasErrors = true;
        } else {
            showFieldValidation(signUpUsernameValidation, true, "Nome de usuário válido");
        }

        // Validar email
        if (!validateEmail(email)) {
            showEmailValidation(signUpEmailValidation, false, "Formato de email inválido");
            hasErrors = true;
        } else {
            showEmailValidation(signUpEmailValidation, true, "Email válido");
        }

        // Validar senha
        if (!validatePassword(password)) {
            showFieldValidation(signUpPasswordValidation, false, "Senha deve ter pelo menos 6 caracteres");
            hasErrors = true;
        } else {
            showFieldValidation(signUpPasswordValidation, true, "Senha válida");
        }

        // Validar confirmação de senha
        if (password !== confirmPassword) {
            showFieldValidation(signUpConfirmPasswordValidation, false, "Senhas não coincidem");
            hasErrors = true;
        } else if (confirmPassword.length > 0) {
            showFieldValidation(signUpConfirmPasswordValidation, true, "Senhas coincidem");
        }

        // Validar aceite dos termos
        if (!acceptTerms) {
            displayMessage("Você deve aceitar os Termos de Serviço e Política de Privacidade.", true);
            hasErrors = true;
        }

        if (hasErrors) {
            return;
        }

        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        if (signUpSubmitButton) signUpSubmitButton.disabled = true;

        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);

            // Aqui você pode salvar informações adicionais do usuário no Firestore
            // Por exemplo: nome completo, nome de usuário, etc.

            displayMessage("Cadastro bem-sucedido! Você está logado.", false);

            // Limpar formulário
            signUpForm.reset();
            hideFieldValidation(signUpFullNameValidation);
            hideFieldValidation(signUpUsernameValidation);
            hideEmailValidation(signUpEmailValidation);
            hideFieldValidation(signUpPasswordValidation);
            hideFieldValidation(signUpConfirmPasswordValidation);

        } catch (error) {
            console.error("Erro no cadastro:", error);
            let errorMessage = `Erro no cadastro: ${error.message}`;

            // Tratamento específico para diferentes tipos de erro
            if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Erro no cadastro: Firebase: Error (auth/operation-not-allowed). " +
                              "O cadastro por e-mail/senha não está habilitado no projeto Firebase. " +
                              "Para resolver: 1) Acesse o Firebase Console, 2) Vá em Authentication > Sign-in method, " +
                              "3) Habilite 'Email/Password' como provedor de login.";
            } else if (error.code === 'auth/email-already-in-use') {
                errorMessage = "Este email já está em uso. Tente fazer login ou use outro email.";
            } else if (error.code === 'auth/weak-password') {
                errorMessage = "Senha muito fraca. Use pelo menos 6 caracteres.";
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = "Email inválido. Verifique o formato do email.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
            if (signUpSubmitButton) signUpSubmitButton.disabled = false;
        }
    });
}

if (googleSignInButton) {
    googleSignInButton.addEventListener('click', async () => {
        if (!auth || !googleProvider) { displayMessage("Firebase Auth não inicializado.", true); return; }
        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (shortenButton) shortenButton.disabled = true;
        try {
            await signInWithPopup(auth, googleProvider);
            displayMessage("Login com Google bem-sucedido!", false);
        } catch (error) {
            console.error("Erro no login com Google:", error);
            let errorMessage = "Erro ao fazer login com Google.";

            // Tratamento específico para diferentes tipos de erro do Google
            if (error.code === 'auth/popup-closed-by-user') {
                errorMessage = "Login cancelado. A janela do Google foi fechada.";
            } else if (error.code === 'auth/popup-blocked') {
                errorMessage = "Pop-up bloqueado pelo navegador. Permita pop-ups para este site.";
            } else if (error.code === 'auth/cancelled-popup-request') {
                errorMessage = "Solicitação de login cancelada. Tente novamente.";
            } else if (error.code === 'auth/operation-not-allowed') {
                errorMessage = "Login com Google não está habilitado. Entre em contato com o suporte.";
            } else if (error.code === 'auth/unauthorized-domain') {
                errorMessage = "Domínio não autorizado para login com Google.";
            } else if (error.code === 'auth/account-exists-with-different-credential') {
                errorMessage = "Já existe uma conta com este email usando outro método de login.";
            } else if (error.code === 'auth/network-request-failed') {
                errorMessage = "Erro de conexão. Verifique sua internet e tente novamente.";
            }

            displayMessage(errorMessage, true);
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            if (shortenButton) shortenButton.disabled = false;
        }
    });
}

if (logoutButton) {
    logoutButton.addEventListener('click', async () => {
        if (!auth) { displayMessage("Firebase Auth não inicializado.", true); return; }
        try {
            await signOut(auth);
            displayMessage("Logout bem-sucedido.", false);
        } catch (error) {
            console.error("Erro no logout:", error);
            displayMessage("Erro ao fazer logout.", true);
        }
    });
}

// --- Histórico de URLs ---
if (showHistoryButton) {
    showHistoryButton.addEventListener('click', async () => {
        if (historySection) historySection.classList.remove('hidden');
        await loadUserHistory();
    });
}

if (hideHistoryButton) {
    hideHistoryButton.addEventListener('click', () => {
        if (historySection) historySection.classList.add('hidden');
    });
}

// --- Funções de Histórico ---
async function loadUserHistory() {
    console.log("loadUserHistory chamada - currentUserId:", currentUserId, "isUserAnonymous:", isUserAnonymous);

    if (!auth || !db) {
        console.error("Firebase não inicializado");
        if (historyEmpty) {
            historyEmpty.textContent = "Erro de configuração. Firebase não inicializado.";
            historyEmpty.classList.remove('hidden');
        }
        if (historyLoading) historyLoading.classList.add('hidden');
        return;
    }

    if (!currentUserId || isUserAnonymous) {
        console.log("Usuário não logado ou anônimo");
        if (historyEmpty) {
            historyEmpty.textContent = "Faça login para ver seu histórico de URLs.";
            historyEmpty.classList.remove('hidden');
        }
        if (historyLoading) historyLoading.classList.add('hidden');
        return;
    }

    try {
        if (historyLoading) historyLoading.classList.remove('hidden');
        if (historyEmpty) historyEmpty.classList.add('hidden');
        if (historyList) historyList.innerHTML = '';

        // Query para buscar URLs do usuário
        const linksCollection = collection(db, "public", "data", "links");
        const userLinksQuery = query(
            linksCollection,
            where("creatorUserId", "==", currentUserId),
            orderBy("createdAt", "desc")
        );

        const querySnapshot = await getDocs(userLinksQuery);

        if (historyLoading) historyLoading.classList.add('hidden');

        if (querySnapshot.empty) {
            if (historyEmpty) historyEmpty.classList.remove('hidden');
            return;
        }

        const urls = [];
        querySnapshot.forEach((doc) => {
            urls.push({ id: doc.id, ...doc.data() });
        });

        // Atualizar estatísticas
        updateStatsOverview(urls);

        // Criar gráfico de analytics
        createClicksChart(urls);

        displayUserHistory(urls);

        return urls; // Retorna para uso na exportação

    } catch (error) {
        console.error("Erro ao carregar histórico:", error);
        console.error("Detalhes do erro:", error.message, error.code);
        if (historyLoading) historyLoading.classList.add('hidden');
        if (historyEmpty) {
            let errorMessage = "Erro ao carregar histórico. Tente novamente.";

            // Tratamento específico para diferentes tipos de erro
            if (error.code === 'failed-precondition') {
                errorMessage = "Índice do banco de dados não configurado. Criando URLs primeiro...";
            } else if (error.code === 'permission-denied') {
                errorMessage = "Permissão negada. Faça login novamente.";
            } else if (error.code === 'unavailable') {
                errorMessage = "Serviço temporariamente indisponível. Tente em alguns minutos.";
            }

            historyEmpty.textContent = errorMessage;
            historyEmpty.classList.remove('hidden');
        }

        // Exibe mensagem de erro também na interface principal
        displayMessage("Erro ao carregar histórico. Verifique o console para detalhes.", true);
    }
}

function displayUserHistory(urls) {
    if (!historyList) return;

    historyList.innerHTML = '';

    urls.forEach((urlData) => {
        const urlItem = createHistoryItem(urlData);
        historyList.appendChild(urlItem);
    });
}

function createHistoryItem(urlData) {
    const item = document.createElement('div');
    item.className = 'bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300';

    const createdDate = urlData.createdAt ?
        new Date(urlData.createdAt.seconds * 1000).toLocaleDateString('pt-BR') :
        'Data não disponível';

    const lastAccessedDate = urlData.lastAccessed ?
        new Date(urlData.lastAccessed.seconds * 1000).toLocaleDateString('pt-BR') :
        'Nunca acessado';

    const clickCount = urlData.clickCount || 0;

    item.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    🔗 encurt.ar/${urlData.shortCode}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400 truncate mt-1" title="${urlData.longUrl}">
                    ${urlData.longUrl}
                </p>
                <div class="flex flex-wrap gap-2 mt-2 text-xs text-gray-400 dark:text-gray-500">
                    <span class="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-2 py-1 rounded">
                        📅 ${createdDate}
                    </span>
                    <span class="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-2 py-1 rounded">
                        👆 ${clickCount} cliques
                    </span>
                    <span class="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 px-2 py-1 rounded">
                        ⏰ ${lastAccessedDate}
                    </span>
                    ${urlData.isCustom ? '<span class="bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 px-2 py-1 rounded">⭐ Personalizado</span>' : ''}
                </div>
            </div>
            <div class="flex space-x-2 ml-4">
                <button onclick="copyUrlFromHistory('${urlData.shortCode}')"
                        class="bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium px-3 py-1 rounded-lg transition-all duration-300 tooltip">
                    📋 Copiar
                </button>
                <button onclick="deleteUrlFromHistory('${urlData.id}', '${urlData.shortCode}')"
                        class="bg-red-500 hover:bg-red-600 text-white text-sm font-medium px-3 py-1 rounded-lg transition-all duration-300 tooltip">
                    🗑️ Excluir
                </button>
            </div>
        </div>
    `;

    return item;
}

// Funções globais para os botões do histórico
window.copyUrlFromHistory = function(shortCode) {
    const fullUrl = `https://encurt.ar/${shortCode}`;

    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(fullUrl).then(() => {
            displayMessage("URL copiada para a área de transferência!", false);
        }).catch(err => {
            console.error('Erro ao copiar:', err);
            legacyCopy(fullUrl);
        });
    } else {
        legacyCopy(fullUrl);
    }
};

window.deleteUrlFromHistory = async function(docId, shortCode) {
    if (!confirm(`Tem certeza que deseja excluir a URL encurt.ar/${shortCode}?`)) {
        return;
    }

    try {
        const docRef = doc(db, "public", "data", "links", docId);
        await deleteDoc(docRef);
        displayMessage("URL excluída com sucesso!", false);
        await loadUserHistory(); // Recarrega o histórico
    } catch (error) {
        console.error("Erro ao excluir URL:", error);
        displayMessage("Erro ao excluir URL. Tente novamente.", true);
    }
};

// --- Lógica de Encurtamento de Link ---
function generateShortCode(length = 7) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function displayShortUrl(shortCode, customPrefix = 'encurt.ar/') {
    const shortUrl = `${customPrefix}${shortCode}`;
    const fullUrl = `https://${shortUrl}`;

    if (shortUrlText) {
        shortUrlText.textContent = shortUrl;
        shortUrlText.href = fullUrl;
    }

    // Gerar QR Code
    generateQRCode(fullUrl);

    if (resultDiv) resultDiv.classList.remove('hidden');
}

if (shortenButton) {
    shortenButton.addEventListener('click', async () => {
        if (!auth || !db) {
            displayMessage("Firebase não inicializado. Encurtamento indisponível.", true);
            return;
        }

        if (!currentUserId) {
            if (auth.currentUser) {
                currentUserId = auth.currentUser.uid;
                isUserAnonymous = auth.currentUser.isAnonymous;
                updateUIForAuthState(auth.currentUser); // Garante que a UI esteja atualizada
            } else {
                try {
                    console.log("Tentando autenticação anônima para encurtar (currentUserId nulo)...");
                    const userCredential = await signInAnonymously(auth);
                    // onAuthStateChanged deve ter sido chamado, mas para garantir:
                    currentUserId = userCredential.user.uid;
                    isUserAnonymous = true;
                    updateUIForAuthState(userCredential.user);
                    console.log("Autenticado anonimamente para encurtar (currentUserId era nulo):", currentUserId);
                } catch (error) {
                    console.error("Erro na autenticação anônima durante o encurtamento (currentUserId nulo):", error);
                    displayMessage("Erro de autenticação ao tentar encurtar. Recarregue.", true);
                    return;
                }
            }
        }
        if (!currentUserId) { // Verificação final
            displayMessage("Não foi possível obter ID do usuário. Tente novamente.", true);
            return;
        }

        let longUrl = longUrlInput.value.trim();
        if (!longUrl) {
            displayMessage("Por favor, insira uma URL válida.", true);
            return;
        }

        // Validação rigorosa de URL
        if (!validateUrl(longUrl)) {
            displayMessage("Por favor, insira uma URL válida. Exemplo: https://www.google.com", true);
            return;
        }

        // Impedir encurtamento de URLs do próprio domínio
        if (longUrl.includes('encurt.ar')) {
            displayMessage("Não é possível encurtar URLs do próprio domínio encurt.ar", true);
            return;
        }

        // Adiciona protocolo se não tiver
        if (!longUrl.startsWith('http://') && !longUrl.startsWith('https://')) {
            longUrl = 'https://' + longUrl;
        }

        if (loadingIndicator) loadingIndicator.classList.remove('hidden');
        if (resultDiv) resultDiv.classList.add('hidden');
        shortenButton.disabled = true;

        let shortCodeToUse = "";
        let isCustomCode = false;
        const customCodeValue = customShortCodeInput ? customShortCodeInput.value.trim() : "";

        if (!isUserAnonymous && customCodeValue !== "") {
            shortCodeToUse = customCodeValue.replace(/[^a-z0-9-]/gi, '').toLowerCase(); // Permite apenas letras, números, hífens
            if (shortCodeToUse !== customCodeValue) {
                 displayMessage("Código personalizado contém caracteres inválidos. Use apenas letras, números e hífens.", true);
                 if (loadingIndicator) loadingIndicator.classList.add('hidden');
                 shortenButton.disabled = false;
                 return;
            }
            if (shortCodeToUse.length === 0 || shortCodeToUse.length > 20) {
                 displayMessage("Código personalizado deve ter entre 1 e 20 caracteres.", true);
                 if (loadingIndicator) loadingIndicator.classList.add('hidden');
                 shortenButton.disabled = false;
                 return;
            }
            isCustomCode = true;
        } else {
            shortCodeToUse = generateShortCode();
        }

        try {
            const linkDocRef = doc(db, "public", "data", "links", shortCodeToUse);
            const docSnap = await getDoc(linkDocRef);

            if (docSnap.exists()) {
                console.warn("Código curto já existe:", shortCodeToUse);
                displayMessage(isCustomCode ? "Este código personalizado já está em uso. Tente outro." : "Ocorreu um erro (colisão de código). Tente novamente.", true);
                throw new Error("Código curto já em uso");
            }

            await setDoc(linkDocRef, {
                longUrl: longUrl,
                shortCode: shortCodeToUse,
                createdAt: serverTimestamp(),
                creatorUserId: currentUserId,
                isAnonymousLink: isUserAnonymous,
                isCustom: isCustomCode,
                clickCount: 0,
                lastAccessed: null
            });

            displayShortUrl(shortCodeToUse);
            if (longUrlInput) longUrlInput.value = '';
            if (customShortCodeInput) customShortCodeInput.value = '';
            displayMessage("URL encurtada com sucesso!", false);

        } catch (error) {
            console.error("Erro ao salvar no Firestore: ", error);
            if (!error.message.includes("Código curto já em uso")) {
                displayMessage("Erro ao encurtar a URL. Tente novamente.", true);
            }
        } finally {
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
            shortenButton.disabled = false;
        }
    });
}

if (copyButton) {
    copyButton.addEventListener('click', () => {
        if (!shortUrlText) return;
        const urlToCopy = `https://${shortUrlText.textContent}`;
        
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(urlToCopy).then(() => {
                displayMessage("URL copiada para a área de transferência!", false);
            }).catch(err => {
                console.error('Erro ao copiar com navigator.clipboard: ', err);
                legacyCopy(urlToCopy);
            });
        } else {
            legacyCopy(urlToCopy);
        }
    });
}

function legacyCopy(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed"; 
    textArea.style.top = "-9999px";
    textArea.style.left = "-9999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        document.execCommand('copy');
        displayMessage("URL copiada para a área de transferência! (fallback)", false);
    } catch (errFallback) {
        console.error('Erro ao copiar texto (fallback): ', errFallback);
        displayMessage("Erro ao copiar. Por favor, copie manualmente.", true);
    }
    document.body.removeChild(textArea);
}

// Ad Placeholder Click Simulation (se você mantiver essa funcionalidade)
const adPlaceholders = document.querySelectorAll('.ad-placeholder');
if (adPlaceholders.length > 0) {
    adPlaceholders.forEach(ad => {
        ad.addEventListener('click', () => {
            const adTextElement = ad.querySelector('p');
            const adText = adTextElement ? adTextElement.textContent : 'Anúncio';
            displayMessage(`Anúncio "${adText}" clicado! (Simulação)`, false);
        });
    });
}




// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, inicializando funcionalidades...');

    // Inicializar dark mode
    initDarkMode();

    // Configurar dark mode toggle
    const darkToggle = document.getElementById('darkModeToggle');
    if (darkToggle) {
        console.log('Botão dark mode encontrado após DOM load');
        darkToggle.addEventListener('click', toggleDarkMode);
    } else {
        console.error('Botão dark mode NÃO encontrado após DOM load');
    }
});

// Chamada inicial para configurar a UI
// Se auth não inicializou (devido a erro de config, por ex.),
// updateUIForAuthState(null) será chamado dentro do bloco onAuthStateChanged ou no catch da inicialização.
if (!auth) { // Garante que a UI seja definida para um estado padrão se auth falhar completamente
    updateUIForAuthState(null);
}

// --- FUNCIONALIDADES DO MENU SUPERIOR ---

// Elementos do menu
const logoButton = document.getElementById('logoButton');
const menuInicio = document.getElementById('menuInicio');
const menuEncurtar = document.getElementById('menuEncurtar');
const menuQRCode = document.getElementById('menuQRCode');
const userMenuButton = document.getElementById('userMenuButton');
const userDropdown = document.getElementById('userDropdown');
const userDropdownContent = document.getElementById('userDropdownContent');
const mobileMenuButton = document.getElementById('mobileMenuButton');
const mobileMenu = document.getElementById('mobileMenu');

// Elementos das seções
const qrcodeSection = document.getElementById('qrcodeSection');
const hideQRCodeButton = document.getElementById('hideQRCodeButton');
const qrCodeUrl = document.getElementById('qrCodeUrl');
const generateQRButton = document.getElementById('generateQRButton');
const qrCodeResult = document.getElementById('qrCodeResult');

// Função para mostrar apenas uma seção
function showSection(sectionToShow) {
    // Ocultar todas as seções
    if (historySection) historySection.classList.add('hidden');
    if (qrcodeSection) qrcodeSection.classList.add('hidden');

    // Se estamos voltando para a seção principal, restaurar estado de autenticação
    if (sectionToShow === 'main' || !sectionToShow) {
        // Ocultar formulários de login/cadastro e mostrar botões de ação
        if (authForms) authForms.classList.add('hidden');
        if (authActions) authActions.classList.remove('hidden');
        if (mainContentSection) mainContentSection.classList.remove('hidden');

        // Garantir que os formulários individuais estejam no estado correto
        if (loginForm) loginForm.classList.remove('hidden');
        if (signUpForm) signUpForm.classList.add('hidden');
    }

    // Mostrar a seção solicitada
    if (sectionToShow && sectionToShow !== 'main') {
        sectionToShow.classList.remove('hidden');
    }

    // Scroll suave para a seção
    if (sectionToShow && sectionToShow !== 'main') {
        sectionToShow.scrollIntoView({ behavior: 'smooth' });
    } else {
        // Scroll para o topo se for a seção principal
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// Event Listeners do Menu
if (logoButton) {
    logoButton.addEventListener('click', () => {
        showSection('main');
    });
}

if (menuInicio) {
    menuInicio.addEventListener('click', () => {
        showSection('main');
    });
}

if (menuEncurtar) {
    menuEncurtar.addEventListener('click', () => {
        showSection('main');
        // Focar no campo de URL
        if (longUrlInput) {
            longUrlInput.focus();
        }
    });
}

if (menuQRCode) {
    menuQRCode.addEventListener('click', () => {
        showSection(qrcodeSection);
    });
}

// Menu mobile
if (mobileMenuButton) {
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });
}

// Event listeners do menu mobile
const mobileMenuInicio = document.getElementById('mobileMenuInicio');
const mobileMenuEncurtar = document.getElementById('mobileMenuEncurtar');
const mobileMenuQRCode = document.getElementById('mobileMenuQRCode');

if (mobileMenuInicio) {
    mobileMenuInicio.addEventListener('click', () => {
        showSection('main');
        mobileMenu.classList.add('hidden');
    });
}

if (mobileMenuEncurtar) {
    mobileMenuEncurtar.addEventListener('click', () => {
        showSection('main');
        if (longUrlInput) longUrlInput.focus();
        mobileMenu.classList.add('hidden');
    });
}

if (mobileMenuQRCode) {
    mobileMenuQRCode.addEventListener('click', () => {
        showSection(qrcodeSection);
        mobileMenu.classList.add('hidden');
    });
}

// Fechar menu mobile ao clicar fora
document.addEventListener('click', (e) => {
    if (mobileMenu && !mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
        mobileMenu.classList.add('hidden');
    }
});

// Menu do usuário
if (userMenuButton) {
    userMenuButton.addEventListener('click', (e) => {
        e.stopPropagation();
        userDropdown.classList.toggle('hidden');
    });
}

// Fechar dropdown do usuário ao clicar fora
document.addEventListener('click', (e) => {
    if (userDropdown && !userDropdown.contains(e.target) && !userMenuButton.contains(e.target)) {
        userDropdown.classList.add('hidden');
    }
});

// Atualizar conteúdo do dropdown do usuário
function updateUserDropdown(user) {
    if (!userDropdownContent) return;

    if (user && !user.isAnonymous) {
        // Usuário logado
        userDropdownContent.innerHTML = `
            <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-200">
                <div class="font-medium">${user.displayName || 'Usuário'}</div>
                <div class="text-gray-500">${user.email}</div>
            </div>
            <button onclick="showUserHistory()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                <svg class="inline h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Meu Histórico
            </button>
            <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                <svg class="inline h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                Sair
            </button>
        `;
    } else if (user && user.isAnonymous) {
        // Usuário anônimo
        userDropdownContent.innerHTML = `
            <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-200">
                <div class="font-medium">Usuário Anônimo</div>
                <div class="text-gray-500">Faça login para mais recursos</div>
            </div>
            <button onclick="showLogin()" class="block w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors">
                <svg class="inline h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                Fazer Login
            </button>
        `;
    } else {
        // Usuário não logado
        userDropdownContent.innerHTML = `
            <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-200">
                <div class="font-medium">Não logado</div>
                <div class="text-gray-500">Entre para acessar recursos</div>
            </div>
            <button onclick="showLogin()" class="block w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors">
                <svg class="inline h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                Fazer Login
            </button>
        `;
    }
}

// Funções globais para o dropdown
window.showUserHistory = function() {
    showSection(historySection);
    loadUserHistory();
    userDropdown.classList.add('hidden');
};

window.showLogin = function() {
    // Ocultar outras seções primeiro
    if (historySection) historySection.classList.add('hidden');
    if (qrcodeSection) qrcodeSection.classList.add('hidden');

    // Configurar formulários de autenticação
    if (authActions) authActions.classList.add('hidden');
    if (authForms) authForms.classList.remove('hidden');
    if (loginForm) loginForm.classList.remove('hidden');
    if (signUpForm) signUpForm.classList.add('hidden');
    if (mainContentSection) mainContentSection.classList.add('hidden');
    userDropdown.classList.add('hidden');

    // Scroll para o topo
    window.scrollTo({ top: 0, behavior: 'smooth' });
};

window.logout = async function() {
    if (!auth) return;
    try {
        await signOut(auth);
        displayMessage("Logout realizado com sucesso.", false);
        userDropdown.classList.add('hidden');
        showSection('main');
    } catch (error) {
        console.error("Erro no logout:", error);
        displayMessage("Erro ao fazer logout.", true);
    }
};

// --- FUNCIONALIDADES DE QR CODE ---

if (hideQRCodeButton) {
    hideQRCodeButton.addEventListener('click', () => {
        showSection('main');
    });
}

if (generateQRButton) {
    generateQRButton.addEventListener('click', async () => {
        const url = qrCodeUrl.value.trim();
        if (!url) {
            displayMessage("Por favor, insira uma URL para gerar o QR Code.", true);
            return;
        }

        // Validar URL
        let finalUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            if (url.includes('encurt.ar/') || url.match(/^[a-zA-Z0-9]{7}$/)) {
                // É um link encurtado
                finalUrl = `https://${url.includes('encurt.ar/') ? url : 'encurt.ar/' + url}`;
            } else {
                finalUrl = 'https://' + url;
            }
        }

        try {
            generateQRButton.disabled = true;
            generateQRButton.innerHTML = `
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Gerando...</span>
            `;

            await generateQRCodeForUrl(finalUrl);
            qrCodeResult.classList.remove('hidden');
            displayMessage("QR Code gerado com sucesso!", false);

        } catch (error) {
            console.error("Erro ao gerar QR Code:", error);
            displayMessage("Erro ao gerar QR Code. Verifique a URL.", true);
        } finally {
            generateQRButton.disabled = false;
            generateQRButton.innerHTML = `
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                </svg>
                <span>Gerar QR Code</span>
            `;
        }
    });
}

// Função para gerar QR Code
async function generateQRCodeForUrl(url) {
    if (!qrcodeDiv || typeof QRCode === 'undefined') {
        throw new Error('QRCode library não carregada');
    }

    // Limpar QR code anterior
    qrcodeDiv.innerHTML = '';

    return new Promise((resolve, reject) => {
        QRCode.toCanvas(qrcodeDiv, url, {
            width: 200,
            height: 200,
            margin: 2,
            color: {
                dark: document.documentElement.classList.contains('dark') ? '#ffffff' : '#000000',
                light: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff'
            }
        }, function (error, canvas) {
            if (error) {
                reject(error);
            } else {
                qrcodeDiv.appendChild(canvas);
                resolve(canvas);
            }
        });
    });
}

// Atualizar dropdown quando o estado de autenticação mudar
const originalUpdateUIForAuthState = updateUIForAuthState;
updateUIForAuthState = function(user) {
    originalUpdateUIForAuthState(user);
    updateUserDropdown(user);
};
