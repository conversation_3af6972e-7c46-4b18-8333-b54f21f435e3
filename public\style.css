body {
    font-family: 'Inter', sans-serif;
}

/* Estilos customizados para glassmorphism e animações */
.glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark .glass {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-bg {
  background: linear-gradient(-45deg, #0ea5e9, #6366f1, #8b5cf6, #3b82f6);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

.tooltip {
  position: relative;
}

.tooltip-text {
  visibility: hidden;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  text-align: center;
  padding: 5px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.loader-dots {
  display: inline-flex;
  gap: 4px;
}

.loader-dots div {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loader-bounce 1.4s infinite ease-in-out both;
}

.loader-dots div:nth-child(1) { animation-delay: -0.32s; }
.loader-dots div:nth-child(2) { animation-delay: -0.16s; }

@keyframes loader-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Transições suaves para o dark mode */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Scrollbar customizada */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Animação para o botão de copiar */
.copy-success {
  animation: copyPulse 0.6s ease;
}

@keyframes copyPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Estilo para o gráfico */
.chart-container {
  position: relative;
  height: 250px;
  width: 100%;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
