// Service Worker simplificado para PWA do encurt.ar
const CACHE_NAME = 'encurt-ar-v2';
const urlsToCache = [
  '/',
  '/index.html',
  '/style.css',
  '/script.js',
  '/icon.svg',
  '/site.webmanifest'
];

// Instalar Service Worker
self.addEventListener('install', function(event) {
  console.log('Service Worker: Instalando...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Service Worker: Cache aberto');
        return cache.addAll(urlsToCache.map(url => new Request(url, {cache: 'reload'})));
      })
      .then(function() {
        console.log('Service Worker: Arquivos adicionados ao cache');
        return self.skipWaiting();
      })
      .catch(function(error) {
        console.error('Service Worker: Erro ao instalar:', error);
      })
  );
});

// Ativar Service Worker
self.addEventListener('activate', function(event) {
  console.log('Service Worker: Ativando...');
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Removendo cache antigo:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(function() {
      console.log('Service Worker: Ativado');
      return self.clients.claim();
    })
  );
});

// Interceptar requisições (estratégia simples)
self.addEventListener('fetch', function(event) {
  // Só processar requisições GET do mesmo domínio
  if (event.request.method !== 'GET' ||
      !event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Ignorar requisições para APIs externas
  if (event.request.url.includes('firebase') ||
      event.request.url.includes('google') ||
      event.request.url.includes('gstatic') ||
      event.request.url.startsWith('chrome-extension://')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Se encontrou no cache, retorna
        if (response) {
          console.log('Service Worker: Servindo do cache:', event.request.url);
          return response;
        }

        // Se não encontrou, busca na rede
        console.log('Service Worker: Buscando na rede:', event.request.url);
        return fetch(event.request)
          .then(function(response) {
            // Se a resposta não é válida, retorna ela mesmo
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clona a resposta para o cache
            const responseToCache = response.clone();

            // Adiciona ao cache de forma assíncrona (não bloqueia)
            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseToCache)
                  .catch(function(error) {
                    console.warn('Service Worker: Erro ao cachear:', error);
                  });
              });

            return response;
          })
          .catch(function(error) {
            console.error('Service Worker: Erro na rede:', error);
            // Tenta retornar a página principal como fallback
            return caches.match('/index.html');
          });
      })
  );
});
